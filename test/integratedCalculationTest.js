/**
 * Integrated Calculation Test
 * 
 * This test validates that the travel allowance properly integrates with
 * overall payroll calculations (PAYE, gross pay, net pay).
 */

const CalculatorEnhancer = require('../utils/calculatorEnhancer');

function testIntegratedCalculations() {
  console.log('🧮 Testing Integrated Travel Allowance Calculations\n');
  console.log('=' .repeat(60));

  const enhancer = new CalculatorEnhancer();

  // Test scenario: Employee with travel allowance
  const testPayrollData = {
    basicSalary: 30000,
    commission: 2000,
    travelAllowance: {
      fixedAllowanceAmount: 4000,
      only20PercentTax: false // 80% taxable = R 3,200
    },
    medicalAid: {
      employeeContribution: 1500
    },
    garnishee: 500
  };

  const testEmployee = {
    _id: 'test-emp',
    firstName: 'John',
    lastName: 'Doe',
    payFrequency: { frequency: 'monthly' }
  };

  const testPeriod = {
    _id: 'test-period',
    basicSalary: 30000,
    PAYE: 5000, // Base PAYE without travel allowance consideration
    UIF: 177.12,
    SDL: 300,
    totalDeductions: 5477.12,
    netPay: 24522.88
  };

  console.log('📋 Test Scenario:');
  console.log(`Basic Salary: R ${testPayrollData.basicSalary.toFixed(2)}`);
  console.log(`Commission: R ${testPayrollData.commission.toFixed(2)}`);
  console.log(`Travel Allowance: R ${testPayrollData.travelAllowance.fixedAllowanceAmount.toFixed(2)}`);
  console.log(`  - Taxable (80%): R ${(testPayrollData.travelAllowance.fixedAllowanceAmount * 0.8).toFixed(2)}`);
  console.log(`  - Non-taxable (20%): R ${(testPayrollData.travelAllowance.fixedAllowanceAmount * 0.2).toFixed(2)}`);

  try {
    // Process enhanced calculator data
    const enhanced = enhancer.processCalculatorData(testPayrollData, testEmployee, testPeriod);

    console.log('\n📊 Enhanced Calculation Results:');
    console.log('-' .repeat(40));

    // Gross Pay Test
    const expectedGrossPay = 30000 + 2000 + 4000; // Basic + Commission + Full Travel Allowance
    console.log(`Gross Pay: R ${enhanced.summary.grossPay.toFixed(2)}`);
    console.log(`Expected: R ${expectedGrossPay.toFixed(2)}`);
    console.log(`✅ Gross Pay ${Math.abs(enhanced.summary.grossPay - expectedGrossPay) < 0.01 ? 'CORRECT' : 'INCORRECT'}`);

    // Taxable Income Test
    const expectedTaxableIncome = 30000 + 2000 + 3200; // Basic + Commission + Taxable Travel
    console.log(`\nTaxable Income: R ${enhanced.summary.breakdown.totalTaxableIncome.toFixed(2)}`);
    console.log(`Expected: R ${expectedTaxableIncome.toFixed(2)}`);
    console.log(`✅ Taxable Income ${Math.abs(enhanced.summary.breakdown.totalTaxableIncome - expectedTaxableIncome) < 0.01 ? 'CORRECT' : 'INCORRECT'}`);

    // Travel Allowance Integration Test
    console.log(`\n🚗 Travel Allowance Integration:`);
    console.log(`Total Amount: R ${enhanced.summary.breakdown.travelAllowance.total.toFixed(2)}`);
    console.log(`Taxable Portion: R ${enhanced.summary.breakdown.travelAllowance.taxable.toFixed(2)}`);
    console.log(`Non-taxable Portion: R ${enhanced.summary.breakdown.travelAllowance.nonTaxable.toFixed(2)}`);
    console.log(`Additional PAYE: R ${enhanced.summary.breakdown.deductions.travelAllowanceImpact.additionalPAYE.toFixed(2)}`);

    // Enhanced Deductions Test
    console.log(`\n💸 Enhanced Deductions:`);
    console.log(`Enhanced PAYE: R ${enhanced.summary.breakdown.deductions.paye.toFixed(2)}`);
    console.log(`Enhanced UIF: R ${enhanced.summary.breakdown.deductions.uif.toFixed(2)}`);
    console.log(`Total Deductions: R ${enhanced.summary.totalDeductions.toFixed(2)}`);

    // Net Pay Test
    const expectedNetPay = enhanced.summary.grossPay - enhanced.summary.totalDeductions;
    console.log(`\nNet Pay: R ${enhanced.summary.netPay.toFixed(2)}`);
    console.log(`Calculated: R ${expectedNetPay.toFixed(2)}`);
    console.log(`✅ Net Pay ${Math.abs(enhanced.summary.netPay - expectedNetPay) < 0.01 ? 'CORRECT' : 'INCORRECT'}`);

    // Validation Summary
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 Integration Validation Summary:');
    console.log('=' .repeat(60));

    const grossPayCorrect = Math.abs(enhanced.summary.grossPay - expectedGrossPay) < 0.01;
    const taxableIncomeCorrect = Math.abs(enhanced.summary.breakdown.totalTaxableIncome - expectedTaxableIncome) < 0.01;
    const travelIntegrated = enhanced.summary.breakdown.travelAllowance.taxable > 0;
    const payeImpactCalculated = enhanced.summary.breakdown.deductions.travelAllowanceImpact.additionalPAYE > 0;
    const netPayCorrect = Math.abs(enhanced.summary.netPay - expectedNetPay) < 0.01;

    console.log(`✅ Gross Pay includes full travel allowance: ${grossPayCorrect ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Taxable income includes only taxable portion: ${taxableIncomeCorrect ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Travel allowance properly integrated: ${travelIntegrated ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Additional PAYE calculated: ${payeImpactCalculated ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Net pay calculation correct: ${netPayCorrect ? 'PASS' : 'FAIL'}`);

    const allTestsPassed = grossPayCorrect && taxableIncomeCorrect && travelIntegrated && payeImpactCalculated && netPayCorrect;

    console.log('\n' + '=' .repeat(60));
    if (allTestsPassed) {
      console.log('🎉 ALL INTEGRATION TESTS PASSED!');
      console.log('✅ Travel allowance properly integrates with payroll calculations');
      console.log('✅ PAYE impact correctly calculated');
      console.log('✅ Gross pay and net pay calculations accurate');
      console.log('✅ South African tax compliance maintained');
    } else {
      console.log('❌ SOME INTEGRATION TESTS FAILED');
      console.log('Please review the calculation logic');
    }
    console.log('=' .repeat(60));

    return allTestsPassed;

  } catch (error) {
    console.error('❌ Error during integration test:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Test specific travel allowance scenarios
function testTravelAllowanceScenarios() {
  console.log('\n🚗 Testing Different Travel Allowance Scenarios\n');

  const enhancer = new CalculatorEnhancer();
  const scenarios = [
    {
      name: 'Standard 80/20 Split',
      data: { travelAllowance: { fixedAllowanceAmount: 5000, only20PercentTax: false } },
      expectedTaxable: 4000,
      expectedNonTaxable: 1000
    },
    {
      name: 'Special 20% Tax Rate',
      data: { travelAllowance: { fixedAllowanceAmount: 5000, only20PercentTax: true } },
      expectedTaxable: 1000,
      expectedNonTaxable: 4000
    },
    {
      name: 'No Travel Allowance',
      data: { travelAllowance: { fixedAllowanceAmount: 0 } },
      expectedTaxable: 0,
      expectedNonTaxable: 0
    }
  ];

  scenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.name}`);
    
    const travelData = enhancer.processTravelAllowanceEnhanced(scenario.data);
    
    if (scenario.data.travelAllowance.fixedAllowanceAmount === 0) {
      console.log(`   Result: ${travelData === null ? '✅ Correctly returns null' : '❌ Should return null'}`);
    } else {
      const taxableCorrect = Math.abs(travelData.taxableAmount - scenario.expectedTaxable) < 0.01;
      const nonTaxableCorrect = Math.abs(travelData.nonTaxableAmount - scenario.expectedNonTaxable) < 0.01;
      
      console.log(`   Taxable: R ${travelData.taxableAmount.toFixed(2)} (Expected: R ${scenario.expectedTaxable.toFixed(2)}) ${taxableCorrect ? '✅' : '❌'}`);
      console.log(`   Non-taxable: R ${travelData.nonTaxableAmount.toFixed(2)} (Expected: R ${scenario.expectedNonTaxable.toFixed(2)}) ${nonTaxableCorrect ? '✅' : '❌'}`);
    }
  });

  console.log('\n✅ Travel allowance scenarios completed.');
}

// Run tests
if (require.main === module) {
  const integrationSuccess = testIntegratedCalculations();
  testTravelAllowanceScenarios();
  
  process.exit(integrationSuccess ? 0 : 1);
}

module.exports = {
  testIntegratedCalculations,
  testTravelAllowanceScenarios
};
