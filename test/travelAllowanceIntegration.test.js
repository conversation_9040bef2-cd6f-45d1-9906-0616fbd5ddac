/**
 * Travel Allowance Integration Test
 * 
 * This test validates the live integration of the enhanced travel allowance calculator
 * with the actual employee profile page structure.
 */

const fs = require('fs');
const path = require('path');

/**
 * Test that the integration files exist and are properly structured
 */
function testIntegrationFiles() {
  console.log('🔍 Testing Integration Files...\n');

  const requiredFiles = [
    'constants/payComponentRegistry.js',
    'utils/calculatorEnhancer.js',
    'views/partials/enhancedTravelAllowanceCalculator.ejs',
    'views/employeeProfile.ejs'
  ];

  let allFilesExist = true;

  requiredFiles.forEach(filePath => {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath} exists`);
    } else {
      console.log(`❌ ${filePath} missing`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

/**
 * Test that the employee profile includes the enhanced calculator
 */
function testEmployeeProfileIntegration() {
  console.log('\n📄 Testing Employee Profile Integration...\n');

  try {
    const employeeProfilePath = path.join(__dirname, '..', 'views', 'employeeProfile.ejs');
    const content = fs.readFileSync(employeeProfilePath, 'utf8');

    // Check for the enhanced calculator include
    const hasEnhancedInclude = content.includes("include('partials/enhancedTravelAllowanceCalculator'");
    console.log(`Enhanced calculator include: ${hasEnhancedInclude ? '✅ Found' : '❌ Missing'}`);

    // Check that existing calculator is preserved
    const hasExistingCalculator = content.includes('<!-- Calculator Card -->');
    console.log(`Existing calculator preserved: ${hasExistingCalculator ? '✅ Found' : '❌ Missing'}`);

    // Check for travel allowance sections in existing calculator
    const hasTravelAllowanceSection = content.includes('Travel Allowance:');
    console.log(`Travel allowance in existing calculator: ${hasTravelAllowanceSection ? '✅ Found' : '❌ Missing'}`);

    // Check for PAYE calculations
    const hasPAYECalculations = content.includes('PAYE Tax:');
    console.log(`PAYE calculations preserved: ${hasPAYECalculations ? '✅ Found' : '❌ Missing'}`);

    return hasEnhancedInclude && hasExistingCalculator && hasTravelAllowanceSection && hasPAYECalculations;

  } catch (error) {
    console.error('❌ Error reading employee profile:', error.message);
    return false;
  }
}

/**
 * Test the enhanced calculator partial structure
 */
function testEnhancedCalculatorPartial() {
  console.log('\n🎨 Testing Enhanced Calculator Partial...\n');

  try {
    const partialPath = path.join(__dirname, '..', 'views', 'partials', 'enhancedTravelAllowanceCalculator.ejs');
    const content = fs.readFileSync(partialPath, 'utf8');

    // Check for feature flag
    const hasFeatureFlag = content.includes('enableEnhancedTravelAllowance');
    console.log(`Feature flag control: ${hasFeatureFlag ? '✅ Found' : '❌ Missing'}`);

    // Check for safety features
    const hasFallback = content.includes('fallback');
    console.log(`Fallback mechanism: ${hasFallback ? '✅ Found' : '❌ Missing'}`);

    // Check for travel allowance specific elements
    const hasTravelAllowanceDisplay = content.includes('Enhanced Travel Allowance Calculator');
    console.log(`Travel allowance display: ${hasTravelAllowanceDisplay ? '✅ Found' : '❌ Missing'}`);

    // Check for tax breakdown
    const hasTaxBreakdown = content.includes('Taxable Portion') && content.includes('Non-taxable Portion');
    console.log(`Tax breakdown display: ${hasTaxBreakdown ? '✅ Found' : '❌ Missing'}`);

    // Check for validation
    const hasValidation = content.includes('Validation Check');
    console.log(`Validation check: ${hasValidation ? '✅ Found' : '❌ Missing'}`);

    return hasFeatureFlag && hasFallback && hasTravelAllowanceDisplay && hasTaxBreakdown && hasValidation;

  } catch (error) {
    console.error('❌ Error reading enhanced calculator partial:', error.message);
    return false;
  }
}

/**
 * Test calculator enhancer functionality
 */
function testCalculatorEnhancerFunctionality() {
  console.log('\n⚙️ Testing Calculator Enhancer Functionality...\n');

  try {
    const CalculatorEnhancer = require('../utils/calculatorEnhancer');
    const enhancer = new CalculatorEnhancer();

    // Test with realistic travel allowance data
    const testPayrollData = {
      basicSalary: 30000,
      travelAllowance: {
        fixedAllowanceAmount: 3500,
        only20PercentTax: false
      },
      medicalAid: {
        employeeContribution: 1500
      }
    };

    const testEmployee = {
      _id: 'test-employee',
      firstName: 'John',
      lastName: 'Doe',
      payFrequency: { frequency: 'monthly' }
    };

    const testPeriod = {
      _id: 'test-period',
      basicSalary: 30000,
      PAYE: 4200,
      UIF: 177.12,
      SDL: 300,
      totalDeductions: 4677.12,
      netPay: 25322.88
    };

    // Test enhanced processing
    const enhanced = enhancer.processCalculatorData(testPayrollData, testEmployee, testPeriod);
    console.log(`Enhanced data processing: ${enhanced ? '✅ Success' : '❌ Failed'}`);

    // Test travel allowance specific processing
    const travelEnhanced = enhancer.processTravelAllowanceEnhanced(testPayrollData);
    console.log(`Travel allowance enhancement: ${travelEnhanced ? '✅ Success' : '❌ Failed'}`);

    if (travelEnhanced) {
      console.log(`  Total: ${travelEnhanced.formatted.total}`);
      console.log(`  Taxable (${travelEnhanced.taxablePercentage}%): ${travelEnhanced.formatted.taxable}`);
      console.log(`  Non-taxable (${travelEnhanced.nonTaxablePercentage}%): ${travelEnhanced.formatted.nonTaxable}`);
      
      // Validate calculations
      const calculationCorrect = Math.abs(
        (travelEnhanced.taxableAmount + travelEnhanced.nonTaxableAmount) - travelEnhanced.totalAmount
      ) < 0.01;
      console.log(`  Calculation accuracy: ${calculationCorrect ? '✅ Correct' : '❌ Error'}`);
    }

    return enhanced && travelEnhanced;

  } catch (error) {
    console.error('❌ Error testing calculator enhancer:', error.message);
    return false;
  }
}

/**
 * Test South African tax compliance preservation
 */
function testTaxCompliancePreservation() {
  console.log('\n🇿🇦 Testing South African Tax Compliance Preservation...\n');

  try {
    // Check that existing calculation files are not modified
    const calculationFiles = [
      'utils/payrollCalculations.js',
      'models/PayrollPeriod.js',
      'services/PayrollService.js'
    ];

    let compliancePreserved = true;

    calculationFiles.forEach(filePath => {
      const fullPath = path.join(__dirname, '..', filePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Check for key South African tax functions
        const hasPAYECalculation = content.includes('calculatePAYE') || content.includes('calculateEnhancedPAYE');
        const hasUIFCalculation = content.includes('calculateUIF') || content.includes('calculatePeriodUIF');
        const hasSDLCalculation = content.includes('SDL') || content.includes('Skills Development Levy');
        
        console.log(`${filePath}:`);
        console.log(`  PAYE calculations: ${hasPAYECalculation ? '✅ Present' : '❌ Missing'}`);
        console.log(`  UIF calculations: ${hasUIFCalculation ? '✅ Present' : '❌ Missing'}`);
        console.log(`  SDL calculations: ${hasSDLCalculation ? '✅ Present' : '❌ Missing'}`);
        
        if (!hasPAYECalculation || !hasUIFCalculation) {
          compliancePreserved = false;
        }
      } else {
        console.log(`❌ ${filePath} not found`);
        compliancePreserved = false;
      }
    });

    return compliancePreserved;

  } catch (error) {
    console.error('❌ Error testing tax compliance:', error.message);
    return false;
  }
}

/**
 * Run all integration tests
 */
function runIntegrationTests() {
  console.log('🚀 Running Travel Allowance Integration Tests\n');
  console.log('=' .repeat(60));

  const results = {
    files: testIntegrationFiles(),
    profile: testEmployeeProfileIntegration(),
    partial: testEnhancedCalculatorPartial(),
    enhancer: testCalculatorEnhancerFunctionality(),
    compliance: testTaxCompliancePreservation()
  };

  console.log('\n' + '=' .repeat(60));
  console.log('📊 Integration Test Results:');
  console.log('=' .repeat(60));

  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${test.padEnd(20)}: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
  });

  const allPassed = Object.values(results).every(result => result);

  console.log('\n' + '=' .repeat(60));
  if (allPassed) {
    console.log('🎉 ALL INTEGRATION TESTS PASSED!');
    console.log('✅ Enhanced Travel Allowance Calculator is ready for production');
    console.log('🔒 All existing functionality preserved');
    console.log('🇿🇦 South African tax compliance maintained');
    console.log('🚗 Travel allowance calculations enhanced with detailed breakdowns');
  } else {
    console.log('❌ SOME TESTS FAILED');
    console.log('Please review the failed tests before proceeding with integration');
  }
  console.log('=' .repeat(60));

  return allPassed;
}

// Run tests if this file is executed directly
if (require.main === module) {
  const success = runIntegrationTests();
  process.exit(success ? 0 : 1);
}

module.exports = {
  runIntegrationTests,
  testIntegrationFiles,
  testEmployeeProfileIntegration,
  testEnhancedCalculatorPartial,
  testCalculatorEnhancerFunctionality,
  testTaxCompliancePreservation
};
