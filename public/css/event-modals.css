/* Event Management Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20px;
  box-sizing: border-box;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 1.25rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 20px -5px rgba(0, 0, 0, 0.1);
  max-width: 520px;
  width: 100%;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  transform: translateY(-20px) scale(0.95);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin: auto;
  position: relative;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.modal.show .modal-content {
  transform: translateY(0) scale(1);
}

.modal-header {
  padding: 2rem 2rem 1.5rem;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 1.25rem 1.25rem 0 0;
  position: relative;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 2rem;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #6366f1, #818cf8);
  border-radius: 1px;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
}

.modal-header h2 i {
  color: #6366f1;
  font-size: 1.75rem;
  background: rgba(99, 102, 241, 0.1);
  padding: 0.5rem;
  border-radius: 0.75rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.1);
}

.modal-body {
  padding: 2rem;
  background: #ffffff;
}

.modal-footer {
  padding: 1.5rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0 0 1.25rem 1.25rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.form-group label i {
  color: #6366f1;
  font-size: 1rem;
}

.form-control {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1.5px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Inter', sans-serif;
  background: #ffffff;
  font-weight: 500;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: #9ca3af;
}

/* Button Styles */
.btn {
  padding: 0.875rem 1.5rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #818cf8);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 1.5px solid #e2e8f0;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: #f8fafc;
  color: #475569;
  border-color: #6366f1;
  transform: translateY(-2px);
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
}

/* Event Details Styles */
.event-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.detail-item label i {
  color: #6366f1;
}

.detail-item span {
  color: #1e293b;
  font-size: 0.875rem;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* Priority Badge */
.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 0.75rem !important;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.priority-low {
  background: #dcfce7 !important;
  color: #166534 !important;
  border-color: #bbf7d0 !important;
}

.priority-medium {
  background: #fef3c7 !important;
  color: #92400e !important;
  border-color: #fde68a !important;
}

.priority-high {
  background: #fee2e2 !important;
  color: #991b1b !important;
  border-color: #fecaca !important;
}

/* Toast Container Positioning */
#toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modal {
    padding: 10px;
  }

  .modal-content {
    width: 100%;
    max-width: none;
    max-height: calc(100vh - 20px);
    margin: 0;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  #toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .modal {
    padding: 15px;
  }

  .modal-content {
    width: 90%;
    max-width: 600px;
  }
}

/* Animation for modal backdrop click */
.modal-content:active {
  transform: scale(0.98);
}

/* Improved focus styles for accessibility */
.modal-close:focus,
.form-control:focus,
.btn:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}
