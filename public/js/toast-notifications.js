/**
 * PandaPayroll Toast Notification System
 * Follows established design system and user preferences:
 * - Top-right positioning
 * - Auto-dismiss after 3-4 seconds
 * - Smooth animations
 * - Color coding (green/red/yellow/blue)
 * - Manual close buttons
 * - Stacking capability
 */

class ToastNotificationSystem {
    constructor() {
        this.container = null;
        this.toasts = [];
        this.initialized = false;

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            // DOM is already ready
            this.init();
        }
    }

    init() {
        // Prevent multiple initializations
        if (this.initialized) return;

        // Ensure document.body exists
        if (!document.body) {
            console.warn('ToastNotificationSystem: document.body not available, retrying...');
            setTimeout(() => this.init(), 10);
            return;
        }

        // Create toast container if it doesn't exist
        this.container = document.getElementById('toast-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'toast-container';
            document.body.appendChild(this.container);
        }

        // Add CSS if not already present
        this.addStyles();
        this.initialized = true;
    }

    addStyles() {
        if (document.getElementById('toast-notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'toast-notification-styles';
        style.textContent = `
            .toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                pointer-events: none;
            }

            .toast-notification {
                display: flex;
                align-items: center;
                gap: 12px;
                min-width: 300px;
                max-width: 400px;
                margin-bottom: 10px;
                padding: 16px 20px;
                border-radius: 8px;
                background: #ffffff;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transform: translateX(120%);
                transition: all 0.3s ease-in-out;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                font-size: 14px;
                line-height: 1.4;
                pointer-events: auto;
                border-left: 4px solid #e2e8f0;
            }

            .toast-notification.show {
                transform: translateX(0);
            }

            .toast-notification.hide {
                transform: translateX(120%);
                opacity: 0;
            }

            .toast-notification .toast-icon {
                font-size: 20px;
                flex-shrink: 0;
            }

            .toast-notification .toast-content {
                flex: 1;
                color: #1e293b;
            }

            .toast-notification .toast-title {
                font-weight: 600;
                margin-bottom: 4px;
                color: #0f172a;
            }

            .toast-notification .toast-message {
                color: #64748b;
                font-size: 13px;
            }

            .toast-notification .toast-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #94a3b8;
                cursor: pointer;
                padding: 0;
                margin-left: 8px;
                flex-shrink: 0;
                transition: color 0.2s ease;
            }

            .toast-notification .toast-close:hover {
                color: #64748b;
            }

            /* Success Toast */
            .toast-notification.success {
                border-left-color: #10b981;
            }

            .toast-notification.success .toast-icon {
                color: #10b981;
            }

            /* Error Toast */
            .toast-notification.error {
                border-left-color: #ef4444;
            }

            .toast-notification.error .toast-icon {
                color: #ef4444;
            }

            /* Warning Toast */
            .toast-notification.warning {
                border-left-color: #f59e0b;
            }

            .toast-notification.warning .toast-icon {
                color: #f59e0b;
            }

            /* Info Toast */
            .toast-notification.info {
                border-left-color: #6366f1;
            }

            .toast-notification.info .toast-icon {
                color: #6366f1;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .toast-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                }

                .toast-notification {
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(style);
    }

    show(options) {
        // Ensure the system is initialized before showing toast
        if (!this.initialized) {
            this.init();
            // If still not initialized, queue the toast for later
            if (!this.initialized) {
                setTimeout(() => this.show(options), 100);
                return;
            }
        }

        const {
            type = 'info',
            title = '',
            message = '',
            duration = 4000,
            closable = true
        } = options;

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;

        // Get appropriate icon
        const iconClass = this.getIconClass(type);

        // Build toast content
        let content = `<i class="ph ${iconClass} toast-icon"></i>`;
        content += '<div class="toast-content">';
        
        if (title) {
            content += `<div class="toast-title">${this.escapeHtml(title)}</div>`;
        }
        
        if (message) {
            content += `<div class="toast-message">${this.escapeHtml(message)}</div>`;
        }
        
        content += '</div>';

        if (closable) {
            content += '<button class="toast-close" aria-label="Close"><i class="ph ph-x"></i></button>';
        }

        toast.innerHTML = content;

        // Add close functionality
        if (closable) {
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => this.remove(toast));
        }

        // Add to container and show
        this.container.appendChild(toast);
        this.toasts.push(toast);

        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.remove(toast);
            }, duration);
        }

        return toast;
    }

    remove(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.add('hide');
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            
            // Remove from toasts array
            const index = this.toasts.indexOf(toast);
            if (index > -1) {
                this.toasts.splice(index, 1);
            }
        }, 300);
    }

    getIconClass(type) {
        const icons = {
            success: 'ph-check-circle',
            error: 'ph-x-circle',
            warning: 'ph-warning-circle',
            info: 'ph-info'
        };
        return icons[type] || icons.info;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Clear all toasts
    clearAll() {
        this.toasts.forEach(toast => this.remove(toast));
    }
}

// Create global instance
const toastSystem = new ToastNotificationSystem();

// Global convenience functions - ensure they're always available
function createGlobalFunctions() {
    window.showToast = function(options) {
        if (typeof options === 'string') {
            // Simple string message
            return toastSystem.show({ message: options });
        }
        return toastSystem.show(options);
    };

    window.showSuccessToast = function(message, title = '') {
        return toastSystem.show({ type: 'success', message, title });
    };

    window.showErrorToast = function(message, title = '') {
        return toastSystem.show({ type: 'error', message, title });
    };

    window.showWarningToast = function(message, title = '') {
        return toastSystem.show({ type: 'warning', message, title });
    };

    window.showInfoToast = function(message, title = '') {
        return toastSystem.show({ type: 'info', message, title });
    };
}

// Create functions immediately
createGlobalFunctions();

// Also ensure they're available after DOM loads (in case of any conflicts)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createGlobalFunctions);
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ToastNotificationSystem, toastSystem };
}
