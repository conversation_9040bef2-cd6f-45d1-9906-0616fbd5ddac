/**
 * Payment Restriction Client-Side Script
 * Makes UI elements read-only when payments are overdue by 60+ days
 */
document.addEventListener('DOMContentLoaded', function() {
  // Check if the account is restricted due to overdue payments
  const isRestricted = document.body.getAttribute('data-payment-restricted') === 'true';
  
  if (!isRestricted) return;
  
  console.log('Account restricted due to overdue payment - applying read-only mode');
  
  // Get restriction data from window object
  const restrictionData = window.paymentRestrictionData || {};
  const daysOverdue = restrictionData.daysOverdue || 60;
  const invoiceNumber = restrictionData.invoiceNumber || 'N/A';
  const invoiceAmount = restrictionData.invoiceAmount || '0.00';
  
  // Add a warning banner at the top of the page
  const warningBanner = document.createElement('div');
  warningBanner.className = 'payment-restriction-banner';
  warningBanner.innerHTML = `
    <div class="payment-restriction-content">
      <i class="ph ph-warning-circle"></i>
      <div class="restriction-message">
        <p><strong>Account Restricted:</strong> Payment is ${daysOverdue} days overdue.</p>
        <p>Invoice ${invoiceNumber} (R${invoiceAmount}) is overdue. <a href="/billing/preferences">Make payment now</a> to restore full access.</p>
      </div>
      <button class="restriction-close" onclick="this.parentElement.parentElement.style.display='none'">
        <i class="ph ph-x"></i>
      </button>
    </div>
  `;
  document.body.insertBefore(warningBanner, document.body.firstChild);
  
  // Add restriction styles
  const restrictionStyle = document.createElement('style');
  restrictionStyle.textContent = `
    .payment-restriction-banner {
      background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
      border-bottom: 2px solid #FECACA;
      padding: 1rem;
      color: #991B1B;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .payment-restriction-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
    }
    
    .payment-restriction-content i.ph-warning-circle {
      font-size: 2rem;
      color: #DC2626;
      flex-shrink: 0;
    }
    
    .restriction-message {
      flex-grow: 1;
    }
    
    .restriction-message p {
      margin: 0;
      font-size: 0.875rem;
      line-height: 1.4;
    }
    
    .restriction-message p:first-child {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    .restriction-message a {
      color: #DC2626;
      font-weight: 600;
      text-decoration: underline;
      transition: color 0.2s ease;
    }
    
    .restriction-message a:hover {
      color: #B91C1C;
    }
    
    .restriction-close {
      background: none;
      border: none;
      color: #991B1B;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 0.25rem;
      transition: background-color 0.2s ease;
    }
    
    .restriction-close:hover {
      background-color: rgba(220, 38, 38, 0.1);
    }
    
    .restriction-close i {
      font-size: 1.25rem;
    }
    
    /* Make all form elements look disabled */
    input:not([type="submit"]):not([type="button"]):not([data-payment-action]), 
    select:not([data-payment-action]), 
    textarea:not([data-payment-action]) {
      background-color: #F9FAFB !important;
      border-color: #E5E7EB !important;
      color: #6B7280 !important;
      cursor: not-allowed !important;
      opacity: 0.75 !important;
    }
    
    /* Style buttons to look disabled except for payment-related buttons */
    button:not([data-payment-action]), 
    .btn:not([data-payment-action]), 
    [type="submit"]:not([data-payment-action]), 
    [type="button"]:not([data-payment-action]),
    .action-button:not([data-payment-action]) {
      background-color: #F3F4F6 !important;
      border-color: #E5E7EB !important;
      color: #9CA3AF !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
    }
    
    /* Keep payment-related buttons functional */
    [data-payment-action] {
      opacity: 1 !important;
      pointer-events: auto !important;
      cursor: pointer !important;
    }
    
    /* Add visual cue on hover for restricted elements */
    .restricted-element:hover::after {
      content: "Account restricted - payment ${daysOverdue} days overdue";
      position: absolute;
      bottom: calc(100% + 8px);
      left: 50%;
      transform: translateX(-50%);
      background-color: #1F2937;
      color: white;
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      white-space: nowrap;
      z-index: 50;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    /* Toast notification styles */
    .payment-restriction-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
      border: 1px solid #FECACA;
      border-left: 4px solid #DC2626;
      color: #991B1B;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      z-index: 1001;
      max-width: 400px;
      animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .payment-restriction-toast .toast-content {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
    }
    
    .payment-restriction-toast .toast-icon {
      font-size: 1.25rem;
      color: #DC2626;
      flex-shrink: 0;
      margin-top: 0.125rem;
    }
    
    .payment-restriction-toast .toast-message {
      flex-grow: 1;
    }
    
    .payment-restriction-toast .toast-title {
      font-weight: 600;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }
    
    .payment-restriction-toast .toast-description {
      font-size: 0.8125rem;
      line-height: 1.4;
      margin: 0;
    }
  `;
  document.head.appendChild(restrictionStyle);
  
  // Disable all form elements except payment-related ones
  const formElements = document.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea');
  formElements.forEach(element => {
    if (!element.hasAttribute('data-payment-action')) {
      element.setAttribute('disabled', 'disabled');
      element.setAttribute('readonly', 'readonly');
      element.setAttribute('title', `Account restricted - payment ${daysOverdue} days overdue`);
      element.classList.add('restricted-element');
    }
  });
  
  // Disable all buttons except those related to payments
  const buttons = document.querySelectorAll('button, .btn, [type="submit"], [type="button"], .action-button');
  buttons.forEach(button => {
    if (!button.hasAttribute('data-payment-action')) {
      button.setAttribute('disabled', 'disabled');
      button.setAttribute('title', `Account restricted - payment ${daysOverdue} days overdue`);
      button.classList.add('restricted-element');
      
      // Prevent click events
      button.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        
        // Show toast notification
        showPaymentRestrictionToast();
        
        return false;
      });
    }
  });
  
  // Mark payment-related buttons as functional
  const paymentButtons = document.querySelectorAll('#pay-with-card, .pay-invoice-btn, [href*="/billing/"]');
  paymentButtons.forEach(button => {
    button.setAttribute('data-payment-action', 'true');
    button.removeAttribute('disabled');
    button.classList.remove('restricted-element');
  });
  
  // Override form submissions for non-payment forms
  const forms = document.querySelectorAll('form:not([action*="/billing/"])');
  forms.forEach(form => {
    form.addEventListener('submit', function(event) {
      if (!form.action.includes('/billing/')) {
        event.preventDefault();
        event.stopPropagation();
        
        // Show toast notification
        showPaymentRestrictionToast();
        
        return false;
      }
    });
  });
  
  // Function to show payment restriction toast
  function showPaymentRestrictionToast() {
    // Remove any existing toasts
    const existingToasts = document.querySelectorAll('.payment-restriction-toast');
    existingToasts.forEach(toast => toast.remove());
    
    const toast = document.createElement('div');
    toast.className = 'payment-restriction-toast';
    toast.innerHTML = `
      <div class="toast-content">
        <i class="ph ph-warning-circle toast-icon"></i>
        <div class="toast-message">
          <div class="toast-title">Account Restricted</div>
          <p class="toast-description">Payment is ${daysOverdue} days overdue. Please make payment to restore full access.</p>
        </div>
      </div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-dismiss after 4 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.style.animation = 'slideInRight 0.3s ease-out reverse';
        setTimeout(() => toast.remove(), 300);
      }
    }, 4000);
  }
  
  console.log('Payment restriction read-only mode applied successfully');
});
