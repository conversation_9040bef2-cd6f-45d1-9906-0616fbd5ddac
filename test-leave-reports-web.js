const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Base URL for the application
const BASE_URL = 'http://localhost:3002';
const COMPANY_CODE = 'CEA08B3B';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  }
};

async function testLeaveReportsWeb() {
  console.log('🌐 Testing Leave Reports through Web Interface...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🏢 Company Code: ${COMPANY_CODE}\n`);

  try {
    // Test 1: Leave Days Report
    console.log('📊 Testing Leave Days Report...');
    await testReport('leaveDaysReport', 'Leave Days Report');

    // Test 2: Leave Expiry Report  
    console.log('\n⏰ Testing Leave Expiry Report...');
    await testReport('leaveExpiryReport', 'Leave Expiry Report');

    // Test 3: Leave Report (Request History)
    console.log('\n📝 Testing Leave Report (Request History)...');
    await testReport('leaveReport', 'Leave Report');

    console.log('\n🎉 All leave reports tested successfully!');

  } catch (error) {
    console.error('❌ Error testing leave reports:', error.message);
  }
}

async function testReport(reportType, reportName) {
  const formats = ['excel', 'pdf', 'csv'];
  
  for (const format of formats) {
    try {
      console.log(`  📄 Testing ${reportName} - ${format.toUpperCase()}...`);
      
      const url = `${BASE_URL}/clients/${COMPANY_CODE}/reporting/generate`;
      const params = {
        reportType: reportType,
        format: format,
        // Add some test parameters
        ...(reportType === 'leaveExpiryReport' && {
          startDate: '2025-06-01',
          endDate: '2025-12-31'
        }),
        ...(reportType === 'leaveReport' && {
          startDate: '2024-01-01',
          endDate: '2025-12-31'
        })
      };

      const response = await axios.get(url, {
        ...testConfig,
        params: params,
        responseType: 'arraybuffer' // For binary data (Excel/PDF)
      });

      if (response.status === 200) {
        const contentLength = response.data.length;
        const contentType = response.headers['content-type'];
        
        console.log(`    ✅ ${format.toUpperCase()}: Generated successfully`);
        console.log(`       📏 Size: ${contentLength} bytes`);
        console.log(`       📋 Content-Type: ${contentType}`);
        
        // Save file for verification
        const filename = `test-${reportType}-${format}.${getFileExtension(format)}`;
        const filepath = path.join(__dirname, 'test-reports', filename);
        
        // Create directory if it doesn't exist
        const dir = path.dirname(filepath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        fs.writeFileSync(filepath, response.data);
        console.log(`       💾 Saved: ${filepath}`);
        
        // Validate content
        if (contentLength > 1000) {
          console.log(`       ✅ Content appears valid (${contentLength} bytes)`);
        } else {
          console.log(`       ⚠️  Content seems small (${contentLength} bytes)`);
        }
        
      } else {
        console.log(`    ❌ ${format.toUpperCase()}: Failed with status ${response.status}`);
      }
      
    } catch (error) {
      if (error.response) {
        console.log(`    ❌ ${format.toUpperCase()}: HTTP ${error.response.status} - ${error.response.statusText}`);
        if (error.response.data) {
          const errorText = error.response.data.toString().substring(0, 200);
          console.log(`       Error: ${errorText}...`);
        }
      } else {
        console.log(`    ❌ ${format.toUpperCase()}: ${error.message}`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

function getFileExtension(format) {
  switch (format) {
    case 'excel': return 'xlsx';
    case 'pdf': return 'pdf';
    case 'csv': return 'csv';
    default: return 'txt';
  }
}

// Additional test for report settings
async function testReportSettings() {
  console.log('\n⚙️  Testing Report Settings...');
  
  try {
    const url = `${BASE_URL}/clients/${COMPANY_CODE}/reporting/settings`;
    const response = await axios.get(url, testConfig);
    
    if (response.status === 200) {
      console.log('    ✅ Report settings accessible');
      console.log(`       📏 Response size: ${JSON.stringify(response.data).length} characters`);
    } else {
      console.log(`    ❌ Report settings failed with status ${response.status}`);
    }
  } catch (error) {
    console.log(`    ❌ Report settings error: ${error.message}`);
  }
}

// Test data availability
async function testDataAvailability() {
  console.log('\n📊 Testing Data Availability...');
  
  try {
    // This would require an API endpoint to check data
    // For now, we'll just verify the reports generate with content
    console.log('    ℹ️  Data availability checked through report generation');
  } catch (error) {
    console.log(`    ❌ Data availability check failed: ${error.message}`);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive leave reports testing...\n');
  
  await testLeaveReportsWeb();
  await testReportSettings();
  await testDataAvailability();
  
  console.log('\n📋 Test Summary:');
  console.log('- Leave Days Report: Tested in Excel, PDF, CSV formats');
  console.log('- Leave Expiry Report: Tested in Excel, PDF, CSV formats');
  console.log('- Leave Report (History): Tested in Excel, PDF, CSV formats');
  console.log('- Report Settings: Tested accessibility');
  console.log('\n✅ Testing completed!');
}

// Execute tests
runAllTests().catch(console.error);
