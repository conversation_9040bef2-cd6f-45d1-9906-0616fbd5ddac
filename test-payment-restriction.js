/**
 * Test script for Payment Restriction functionality
 * This script tests the 60-day overdue payment restriction feature
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Invoice = require('./models/invoice');
const Company = require('./models/Company');
const User = require('./models/user');

async function testPaymentRestriction() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a test company (or create one)
    let testCompany = await Company.findOne({ name: { $regex: /test/i } });
    if (!testCompany) {
      console.log('❌ No test company found. Please create a test company first.');
      return;
    }

    console.log(`📋 Using test company: ${testCompany.name} (${testCompany._id})`);

    // Test 1: Check invoice model methods
    console.log('\n🧪 Test 1: Invoice Model Methods');
    
    // Create a test invoice that's 65 days overdue
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 65);
    
    const testInvoice = new Invoice({
      number: `TEST-${Date.now()}`,
      company: testCompany._id,
      date: new Date(),
      dueDate: overdueDate,
      amount: 250.00,
      status: 'pending'
    });

    // Test the methods
    console.log(`   isOverdue(): ${testInvoice.isOverdue()}`);
    console.log(`   shouldRestrictAccount(): ${testInvoice.shouldRestrictAccount()}`);
    console.log(`   getDaysOverdue(): ${testInvoice.getDaysOverdue()}`);

    // Test 2: Check with a current invoice
    console.log('\n🧪 Test 2: Current Invoice (Not Overdue)');
    
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + 14); // Due in 14 days
    
    const currentInvoice = new Invoice({
      number: `CURRENT-${Date.now()}`,
      company: testCompany._id,
      date: new Date(),
      dueDate: currentDate,
      amount: 250.00,
      status: 'pending'
    });

    console.log(`   isOverdue(): ${currentInvoice.isOverdue()}`);
    console.log(`   shouldRestrictAccount(): ${currentInvoice.shouldRestrictAccount()}`);
    console.log(`   getDaysOverdue(): ${currentInvoice.getDaysOverdue()}`);

    // Test 3: Check with a paid invoice
    console.log('\n🧪 Test 3: Paid Invoice');
    
    const paidInvoice = new Invoice({
      number: `PAID-${Date.now()}`,
      company: testCompany._id,
      date: new Date(),
      dueDate: overdueDate,
      amount: 250.00,
      status: 'paid',
      paymentDate: new Date()
    });

    console.log(`   isOverdue(): ${paidInvoice.isOverdue()}`);
    console.log(`   shouldRestrictAccount(): ${paidInvoice.shouldRestrictAccount()}`);
    console.log(`   getDaysOverdue(): ${paidInvoice.getDaysOverdue()}`);

    // Test 4: Save test invoice to database for middleware testing
    console.log('\n🧪 Test 4: Database Operations');
    
    try {
      await testInvoice.save();
      console.log(`   ✅ Test invoice saved: ${testInvoice.number}`);
      
      // Find overdue invoices for the company
      const overdueInvoices = await Invoice.find({
        company: testCompany._id,
        status: { $ne: 'paid' },
        dueDate: { $lt: new Date() }
      }).sort({ dueDate: 1 });

      console.log(`   📊 Found ${overdueInvoices.length} overdue invoices for company`);
      
      if (overdueInvoices.length > 0) {
        const oldestOverdue = overdueInvoices[0];
        console.log(`   📅 Oldest overdue: ${oldestOverdue.number} (${oldestOverdue.getDaysOverdue()} days)`);
        console.log(`   🚫 Should restrict: ${oldestOverdue.shouldRestrictAccount()}`);
      }

    } catch (error) {
      console.error(`   ❌ Error saving test invoice: ${error.message}`);
    }

    // Test 5: Cleanup
    console.log('\n🧹 Cleanup');
    try {
      await Invoice.deleteOne({ _id: testInvoice._id });
      console.log('   ✅ Test invoice cleaned up');
    } catch (error) {
      console.error(`   ❌ Cleanup error: ${error.message}`);
    }

    console.log('\n✅ Payment restriction tests completed!');
    console.log('\n📋 Summary:');
    console.log('   - Invoice model methods working correctly');
    console.log('   - 60+ day overdue detection functional');
    console.log('   - Database queries working');
    console.log('   - Ready for middleware testing');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  testPaymentRestriction();
}

module.exports = { testPaymentRestriction };
