<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Notifications Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="public/js/toast-notifications.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .btn-success {
            background: #10b981;
            color: white;
        }
        .btn-error {
            background: #ef4444;
            color: white;
        }
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        .btn-info {
            background: #6366f1;
            color: white;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .scenario {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .scenario h4 {
            margin: 0 0 10px 0;
            color: #334155;
        }
        .scenario p {
            margin: 0;
            color: #64748b;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🍞 Toast Notifications Test</h1>
        
        <div class="test-section">
            <h3>Basic Toast Types</h3>
            <div class="button-group">
                <button class="btn-success" onclick="showSuccessToast('Operation completed successfully!', 'Success')">Success Toast</button>
                <button class="btn-error" onclick="showErrorToast('Something went wrong. Please try again.', 'Error')">Error Toast</button>
                <button class="btn-warning" onclick="showWarningToast('Please check your input before proceeding.', 'Warning')">Warning Toast</button>
                <button class="btn-info" onclick="showInfoToast('Here is some helpful information.', 'Info')">Info Toast</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Pay Frequency Scenarios</h3>
            
            <div class="scenario">
                <h4>Validation Errors</h4>
                <p>Test validation error messages that would appear during form submission</p>
                <div class="button-group">
                    <button class="btn-warning" onclick="showWarningToast('Please select a frequency', 'Validation Error')">Missing Frequency</button>
                    <button class="btn-warning" onclick="showWarningToast('Please select the last day of period', 'Validation Error')">Missing Last Day</button>
                    <button class="btn-warning" onclick="showWarningToast('Please select the first payroll period end date', 'Validation Error')">Missing End Date</button>
                </div>
            </div>

            <div class="scenario">
                <h4>Security & System Errors</h4>
                <p>Test security and system-level error messages</p>
                <div class="button-group">
                    <button class="btn-error" onclick="showErrorToast('Security token not found. Please refresh the page and try again.', 'Security Error')">CSRF Error</button>
                    <button class="btn-error" onclick="showErrorToast('Network connection failed. Please check your internet connection.', 'Network Error')">Network Error</button>
                </div>
            </div>

            <div class="scenario">
                <h4>Success Messages</h4>
                <p>Test success messages for completed operations</p>
                <div class="button-group">
                    <button class="btn-success" onclick="showSuccessToast('Pay frequency has been saved successfully!', 'Success')">Frequency Saved</button>
                    <button class="btn-success" onclick="showSuccessToast('Pay frequency settings updated successfully!', 'Success')">Settings Updated</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Stacking Test</h3>
            <p>Test multiple notifications appearing at once</p>
            <div class="button-group">
                <button class="btn-info" onclick="testStacking()">Show Multiple Toasts</button>
                <button class="btn-error" onclick="toastSystem.clearAll()">Clear All Toasts</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Custom Duration Test</h3>
            <div class="button-group">
                <button class="btn-info" onclick="showToast({type: 'info', message: 'This toast will disappear in 1 second', duration: 1000})">1 Second</button>
                <button class="btn-info" onclick="showToast({type: 'info', message: 'This toast will stay for 10 seconds', duration: 10000})">10 Seconds</button>
                <button class="btn-info" onclick="showToast({type: 'info', message: 'This toast will not auto-dismiss', duration: 0})">No Auto-dismiss</button>
            </div>
        </div>
    </div>

    <script>
        function testStacking() {
            showSuccessToast('First notification', 'Success');
            setTimeout(() => showWarningToast('Second notification', 'Warning'), 500);
            setTimeout(() => showErrorToast('Third notification', 'Error'), 1000);
            setTimeout(() => showInfoToast('Fourth notification', 'Info'), 1500);
        }

        // Test that the toast system is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof showToast === 'function') {
                console.log('✅ Toast notification system loaded successfully');
                showInfoToast('Toast notification system is ready!', 'System Ready');
            } else {
                console.error('❌ Toast notification system failed to load');
                alert('Toast notification system failed to load');
            }
        });
    </script>
</body>
</html>
