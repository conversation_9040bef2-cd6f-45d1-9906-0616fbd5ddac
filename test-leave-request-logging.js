clear// Test script to verify leave request logging functionality
require('dotenv').config({ path: '.env.development' });

// Set required environment variables
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key';
process.env.SESSION_SECRET = process.env.SESSION_SECRET || 'test-session-secret';
process.env.NODE_ENV = 'development';

// Set WhatsApp environment variables for testing
process.env.WHATSAPP_API_VERSION = process.env.WHATSAPP_API_VERSION || 'v22.0';
process.env.WHATSAPP_PHONE_NUMBER_ID = process.env.WHATSAPP_PHONE_NUMBER_ID || '623930367477322';
process.env.WHATSAPP_ACCESS_TOKEN = process.env.WHATSAPP_ACCESS_TOKEN || 'test-token';

const mongoose = require('mongoose');
const WhatsAppService = require('./services/whatsappService');
const Employee = require('./models/Employee');
const Company = require('./models/Company');
const User = require('./models/User');
const LeaveType = require('./models/LeaveType');
const WhatsAppRequestLog = require('./models/WhatsAppRequestLog');

async function testLeaveRequestLogging() {
  try {
    console.log('🚀 Starting Leave Request Logging Test...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');

    // Find any existing company (use production data)
    console.log('🏢 Finding existing company...');
    const company = await Company.findOne();
    if (!company) {
      throw new Error('No companies found in database. Please run this test against a database with existing data.');
    }
    console.log(`✅ Using company: ${company.name} (${company.companyCode})\n`);

    // Find or create a test employee
    console.log('👤 Finding or creating test employee...');
    let employee = await Employee.findOne({
      company: company._id,
      'personalDetails.mobileNumber': { $exists: true, $ne: null }
    }).populate('company');

    if (!employee) {
      console.log('⚠️ No test employee found, creating one...');
      employee = new Employee({
        company: company._id,
        firstName: 'Test',
        lastName: 'Employee',
        personalDetails: {
          mobileNumber: '27123456789',
          email: '<EMAIL>'
        },
        status: 'Active'
      });
      await employee.save();
      await employee.populate('company');
      console.log('✅ Created test employee');
    }
    console.log(`✅ Using employee: ${employee.firstName} ${employee.lastName} (${employee.personalDetails.mobileNumber})\n`);

    // Find or create a leave type
    console.log('📋 Finding or creating leave type...');
    let leaveType = await LeaveType.findOne({ company: company._id });
    if (!leaveType) {
      console.log('⚠️ No leave type found, creating one...');
      leaveType = new LeaveType({
        company: company._id,
        name: 'Annual Leave',
        category: 'Annual',
        daysAllowed: 21,
        carryOver: true
      });
      await leaveType.save();
      console.log('✅ Created test leave type');
    }
    console.log(`✅ Using leave type: ${leaveType.name}\n`);

    // Initialize WhatsApp service
    const whatsappService = new WhatsAppService();

    // Test 1: Legacy leave request format
    console.log('🧪 Test 1: Testing legacy leave request format...');
    const legacyMessage = `Leave request: ${leaveType.name} from 2024-12-01 to 2024-12-05 reason: Testing leave request logging`;
    
    console.log(`📝 Message: "${legacyMessage}"`);
    const legacyResult = await whatsappService.handleLeaveRequest(employee, legacyMessage);
    console.log(`📊 Result:`, legacyResult);

    // Check if log was created
    const legacyLog = await WhatsAppRequestLog.findOne({
      employee: employee._id,
      company: company._id,
      requestType: 'leave',
      requestText: legacyMessage
    }).sort({ timestamp: -1 });

    if (legacyLog) {
      console.log('✅ Legacy leave request log created successfully!');
      console.log(`   - Status: ${legacyLog.status}`);
      console.log(`   - Timestamp: ${legacyLog.timestamp}`);
      console.log(`   - Response: ${legacyLog.response}`);
    } else {
      console.log('❌ Legacy leave request log NOT created');
    }
    console.log('');

    // Test 2: Send leave type list (modern flow)
    console.log('🧪 Test 2: Testing modern leave request flow (send leave type list)...');
    await whatsappService.sendLeaveTypeList(employee);
    
    // Check if log was created for leave type list
    const listLog = await WhatsAppRequestLog.findOne({
      employee: employee._id,
      company: company._id,
      requestType: 'leave',
      requestText: 'Leave type selection menu requested'
    }).sort({ timestamp: -1 });

    if (listLog) {
      console.log('✅ Leave type list request log created successfully!');
      console.log(`   - Status: ${listLog.status}`);
      console.log(`   - Timestamp: ${listLog.timestamp}`);
      console.log(`   - Response: ${listLog.response}`);
    } else {
      console.log('❌ Leave type list request log NOT created');
    }
    console.log('');

    // Test 3: Check total leave request logs
    console.log('🧪 Test 3: Checking total leave request logs...');
    const totalLogs = await WhatsAppRequestLog.countDocuments({
      company: company._id,
      requestType: 'leave'
    });
    console.log(`📊 Total leave request logs for company: ${totalLogs}`);

    // Show recent leave logs
    const recentLogs = await WhatsAppRequestLog.find({
      company: company._id,
      requestType: 'leave'
    }).sort({ timestamp: -1 }).limit(5);

    console.log('\n📋 Recent leave request logs:');
    recentLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.requestText} - ${log.status} (${log.timestamp})`);
    });

    console.log('\n🎉 Leave Request Logging Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
    process.exit(0);
  }
}

// Run the test
testLeaveRequestLogging();
