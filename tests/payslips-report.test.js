const mongoose = require('mongoose');

// Mock all external dependencies
jest.mock('../models/PayrollPeriod');
jest.mock('../models/Company');
jest.mock('../models/employerDetails');
jest.mock('exceljs');

const PayrollPeriod = require('../models/PayrollPeriod');
const Company = require('../models/Company');
const EmployerDetails = require('../models/employerDetails');
const ReportController = require('../controllers/reportController');

describe('Payslips Report Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should build correct MongoDB aggregation query for payslips', async () => {
    // Mock data
    const mockCompanyId = new mongoose.Types.ObjectId();
    const mockCompany = {
      _id: mockCompanyId,
      name: 'Test Company',
      tradingName: 'Test Trading Name',
      companyCode: 'TEST001'
    };
    const mockEmployerDetails = {
      physicalAddress: '123 Test Street',
      postalAddress: 'PO Box 123'
    };

    // Mock the database calls to return immediately
    Company.findById = jest.fn().mockResolvedValue(mockCompany);
    EmployerDetails.findOne = jest.fn().mockResolvedValue(mockEmployerDetails);
    PayrollPeriod.aggregate = jest.fn().mockResolvedValue([]);

    // Test parameters
    const format = 'excel';
    const settings = {};
    const dateRange = {
      startDate: '2024-01-01',
      endDate: '2024-01-31'
    };

    try {
      // Test with CSV format to avoid Excel generation issues in tests
      await ReportController.generatePayslips(
        mockCompanyId,
        'csv',
        settings,
        dateRange
      );

      // Verify Company.findById was called
      expect(Company.findById).toHaveBeenCalledWith(mockCompanyId);
      
      // Verify EmployerDetails.findOne was called
      expect(EmployerDetails.findOne).toHaveBeenCalledWith({ company: mockCompanyId });

      // Verify PayrollPeriod.aggregate was called
      expect(PayrollPeriod.aggregate).toHaveBeenCalled();
      
      // Get the aggregation pipeline
      const aggregationPipeline = PayrollPeriod.aggregate.mock.calls[0][0];
      
      // Verify the $match stage
      expect(aggregationPipeline[0].$match).toEqual({
        company: mockCompanyId,
        isFinalized: true,
        endDate: {
          $gte: new Date('2024-01-01'),
          $lte: new Date('2024-01-31')
        }
      });

      // Verify employee lookup stage exists
      const employeeLookup = aggregationPipeline.find(stage => 
        stage.$lookup && stage.$lookup.from === 'employees'
      );
      expect(employeeLookup).toBeDefined();
      expect(employeeLookup.$lookup.localField).toBe('employee');
      expect(employeeLookup.$lookup.foreignField).toBe('_id');

    } catch (error) {
      console.error('Test failed:', error);
      throw error;
    }
  });

  test('should handle employee filtering in aggregation query', async () => {
    const mockCompanyId = new mongoose.Types.ObjectId();
    const selectedEmployees = [
      new mongoose.Types.ObjectId(),
      new mongoose.Types.ObjectId()
    ];

    // Mock the database calls
    Company.findById = jest.fn().mockResolvedValue({ _id: mockCompanyId, name: 'Test' });
    EmployerDetails.findOne = jest.fn().mockResolvedValue({});
    PayrollPeriod.aggregate = jest.fn().mockResolvedValue([]);

    await ReportController.generatePayslips(
      mockCompanyId,
      'csv',
      {},
      null,
      selectedEmployees
    );

    // Verify that employee filter was applied in the $match stage
    const aggregationPipeline = PayrollPeriod.aggregate.mock.calls[0][0];
    const matchStage = aggregationPipeline[0].$match;
    
    expect(matchStage.employee).toEqual({
      $in: selectedEmployees.map(id => new mongoose.Types.ObjectId(id))
    });
  });

  test('should handle missing date range gracefully', async () => {
    const mockCompanyId = new mongoose.Types.ObjectId();
    
    Company.findById = jest.fn().mockResolvedValue({ _id: mockCompanyId, name: 'Test' });
    EmployerDetails.findOne = jest.fn().mockResolvedValue({});
    PayrollPeriod.aggregate = jest.fn().mockResolvedValue([]);

    await ReportController.generatePayslips(
      mockCompanyId,
      'csv',
      {},
      null // no date range
    );

    // Verify that no date filter was applied
    const aggregationPipeline = PayrollPeriod.aggregate.mock.calls[0][0];
    const matchStage = aggregationPipeline[0].$match;
    
    expect(matchStage.endDate).toBeUndefined();
    expect(matchStage.company).toEqual(mockCompanyId);
    expect(matchStage.isFinalized).toBe(true);
  });

  test('should validate aggregation pipeline structure', async () => {
    const mockCompanyId = new mongoose.Types.ObjectId();
    
    Company.findById = jest.fn().mockResolvedValue({ _id: mockCompanyId, name: 'Test' });
    EmployerDetails.findOne = jest.fn().mockResolvedValue({});
    PayrollPeriod.aggregate = jest.fn().mockResolvedValue([]);

    await ReportController.generatePayslips(mockCompanyId, 'csv', {}, null);

    const pipeline = PayrollPeriod.aggregate.mock.calls[0][0];
    
    // Verify pipeline has the expected stages
    expect(pipeline).toHaveLength(10); // match, lookup, unwind, match, lookup, unwind, lookup, unwind, project, sort
    
    // Verify stage types
    expect(pipeline[0]).toHaveProperty('$match');
    expect(pipeline[1]).toHaveProperty('$lookup');
    expect(pipeline[2]).toHaveProperty('$unwind');
    expect(pipeline[3]).toHaveProperty('$match'); // employee status filter
    expect(pipeline[4]).toHaveProperty('$lookup'); // company lookup
    expect(pipeline[5]).toHaveProperty('$unwind'); // company unwind
    expect(pipeline[6]).toHaveProperty('$lookup'); // pay frequency lookup
    expect(pipeline[7]).toHaveProperty('$unwind'); // pay frequency unwind
    expect(pipeline[8]).toHaveProperty('$project');
    expect(pipeline[9]).toHaveProperty('$sort');
  });
});
