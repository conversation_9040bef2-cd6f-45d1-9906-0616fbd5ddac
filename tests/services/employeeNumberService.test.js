const mongoose = require('mongoose');
const EmployeeNumberService = require('../../services/employeeNumberService');
const EmployeeNumberSettings = require('../../models/employeeNumberSettings');
const Employee = require('../../models/Employee');
const Company = require('../../models/Company');
const User = require('../../models/user');

// Mock console methods to reduce test output noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

describe('EmployeeNumberService', () => {
  let testCompanyId;
  let testCompany;
  let testUser;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/pandapayroll_test');
    }

    // Create a test user first
    testUser = new User({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'hashedpassword123'
    });
    await testUser.save();

    // Create a test company
    testCompany = new Company({
      name: 'Test Company',
      companyCode: 'TEST001',
      owner: testUser._id
    });
    await testCompany.save();
    testCompanyId = testCompany._id;
  });

  afterAll(async () => {
    // Clean up test data
    await EmployeeNumberSettings.deleteMany({ company: testCompanyId });
    await Employee.deleteMany({ company: testCompanyId });
    await Company.deleteOne({ _id: testCompanyId });
    await User.deleteOne({ _id: testUser._id });

    // Close database connection
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up before each test
    await EmployeeNumberSettings.deleteMany({ company: testCompanyId });
    await Employee.deleteMany({ company: testCompanyId });
  });

  describe('generateEmployeeNumber', () => {
    test('should create default settings and generate first employee number when no settings exist', async () => {
      const employeeNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      
      expect(employeeNumber).toBe('0001');
      
      // Verify settings were created
      const settings = await EmployeeNumberSettings.findOne({ company: testCompanyId });
      expect(settings).toBeTruthy();
      expect(settings.mode).toBe('automatic');
      expect(settings.firstCompanyEmployeeNumber).toBe('0001');
      expect(settings.sequence.current).toBe(1);
      expect(settings.lastGeneratedNumber).toBe('0001');
    });

    test('should generate sequential employee numbers', async () => {
      // Generate first employee number
      const firstNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(firstNumber).toBe('0001');

      // Generate second employee number
      const secondNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(secondNumber).toBe('0002');

      // Generate third employee number
      const thirdNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(thirdNumber).toBe('0003');
    });

    test('should handle custom pattern correctly', async () => {
      // Create settings with custom pattern
      const settings = new EmployeeNumberSettings({
        company: testCompanyId,
        mode: 'automatic',
        firstCompanyEmployeeNumber: 'EMP001'
      });
      settings.parsePattern();
      await settings.save();

      const employeeNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(employeeNumber).toBe('EMP002');
    });

    test('should throw error when mode is manual', async () => {
      // Create settings with manual mode
      const settings = new EmployeeNumberSettings({
        company: testCompanyId,
        mode: 'manual',
        firstCompanyEmployeeNumber: '0001'
      });
      await settings.save();

      await expect(EmployeeNumberService.generateEmployeeNumber(testCompanyId))
        .rejects
        .toThrow('Employee number generation is set to manual mode');
    });

    test('should skip existing employee numbers', async () => {
      // Create an employee with number 0002
      const existingEmployee = new Employee({
        company: testCompanyId,
        firstName: 'Test',
        lastName: 'Employee',
        email: '<EMAIL>',
        companyEmployeeNumber: '0002',
        globalEmployeeId: 'TEST001-0002'
      });
      await existingEmployee.save();

      // Generate first number (should be 0001)
      const firstNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(firstNumber).toBe('0001');

      // Generate second number (should skip 0002 and go to 0003)
      const secondNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      expect(secondNumber).toBe('0003');
    });

    test('should handle invalid pattern gracefully', async () => {
      // Create settings with invalid pattern
      const settings = new EmployeeNumberSettings({
        company: testCompanyId,
        mode: 'automatic',
        firstCompanyEmployeeNumber: 'INVALID'
      });
      
      // This should not throw an error due to our improved error handling
      const employeeNumber = await EmployeeNumberService.generateEmployeeNumber(testCompanyId);
      
      // Should fall back to default pattern
      expect(employeeNumber).toBe('0001');
    });

    test('should handle concurrent requests correctly', async () => {
      // Simulate concurrent requests
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(EmployeeNumberService.generateEmployeeNumber(testCompanyId));
      }

      const results = await Promise.all(promises);
      
      // All results should be unique
      const uniqueResults = [...new Set(results)];
      expect(uniqueResults.length).toBe(5);
      
      // Results should be sequential (though order may vary due to concurrency)
      const sortedResults = results.sort();
      expect(sortedResults).toEqual(['0001', '0002', '0003', '0004', '0005']);
    });
  });

  describe('validateEmployeeNumber', () => {
    test('should validate correct employee number format', async () => {
      const result = await EmployeeNumberService.validateEmployeeNumber(testCompanyId, '0001');
      expect(result.isValid).toBe(true);
      expect(result.message).toBe('Valid employee number');
    });

    test('should reject duplicate employee numbers', async () => {
      // Create an employee with number 0001
      const existingEmployee = new Employee({
        company: testCompanyId,
        firstName: 'Test',
        lastName: 'Employee',
        email: '<EMAIL>',
        companyEmployeeNumber: '0001',
        globalEmployeeId: 'TEST001-0001'
      });
      await existingEmployee.save();

      const result = await EmployeeNumberService.validateEmployeeNumber(testCompanyId, '0001');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Employee number already exists');
    });
  });
});
