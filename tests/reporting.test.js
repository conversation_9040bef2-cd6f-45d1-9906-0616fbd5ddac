const ReportController = require('../controllers/reportController');
const mongoose = require('mongoose');

// Mock the models
jest.mock('../models/PayrollPeriod');
jest.mock('../models/Company');
jest.mock('../models/employerDetails');

const PayrollPeriod = require('../models/PayrollPeriod');
const Company = require('../models/Company');
const EmployerDetails = require('../models/employerDetails');

// Mock ExcelJS to avoid actual file generation in tests
jest.mock('exceljs', () => {
  return {
    Workbook: jest.fn().mockImplementation(() => ({
      addWorksheet: jest.fn().mockReturnValue({
        views: [],
        mergeCells: jest.fn(),
        getCell: jest.fn().mockReturnValue({
          value: '',
          font: {},
          alignment: {},
          border: {},
          fill: {},
          numFmt: ''
        }),
        getRow: jest.fn().mockReturnValue({
          font: {},
          alignment: {},
          height: 0,
          getCell: jest.fn().mockReturnValue({
            value: '',
            border: {},
            fill: {},
            numFmt: ''
          })
        }),
        getColumn: jest.fn().mockReturnValue({
          width: 0
        }),
        autoFilter: {}
      }),
      xlsx: {
        writeBuffer: jest.fn().mockResolvedValue(Buffer.from('mock excel data'))
      }
    }))
  };
});

describe('Excel Report Download Functionality', () => {
  describe('getFileDetails method', () => {
    test('should return correct extension for Excel format', () => {
      const fileDetails = ReportController.getFileDetails('excel');
      
      expect(fileDetails).toEqual({
        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        extension: 'xlsx'
      });
    });

    test('should return correct extension for PDF format', () => {
      const fileDetails = ReportController.getFileDetails('pdf');
      
      expect(fileDetails).toEqual({
        contentType: 'application/pdf',
        extension: 'pdf'
      });
    });

    test('should return correct extension for CSV format', () => {
      const fileDetails = ReportController.getFileDetails('csv');
      
      expect(fileDetails).toEqual({
        contentType: 'text/csv',
        extension: 'csv'
      });
    });

    test('should return default extension for unknown format', () => {
      const fileDetails = ReportController.getFileDetails('unknown');
      
      expect(fileDetails).toEqual({
        contentType: 'application/octet-stream',
        extension: 'txt'
      });
    });
  });

  describe('Frontend file extension mapping', () => {
    test('should map excel format to xlsx extension', () => {
      // Simulate the frontend getFileExtension function
      const getFileExtension = (format) => {
        switch (format) {
          case "pdf":
            return "pdf";
          case "excel":
            return "xlsx";
          case "csv":
            return "csv";
          default:
            return "txt";
        }
      };

      expect(getFileExtension('excel')).toBe('xlsx');
      expect(getFileExtension('pdf')).toBe('pdf');
      expect(getFileExtension('csv')).toBe('csv');
      expect(getFileExtension('unknown')).toBe('txt');
    });

    test('should generate correct filename with proper extension', () => {
      const getFileExtension = (format) => {
        switch (format) {
          case "pdf":
            return "pdf";
          case "excel":
            return "xlsx";
          case "csv":
            return "csv";
          default:
            return "txt";
        }
      };

      const reportType = 'employeeBasicInfo';
      const format = 'excel';
      const date = '2024-01-15';

      const filename = `${reportType}_${date}.${getFileExtension(format)}`;

      expect(filename).toBe('employeeBasicInfo_2024-01-15.xlsx');
      expect(filename).not.toContain('.excel');
    });
  });

  describe('Employment Tax Incentive Report Implementation', () => {
    test('should have employmentTaxIncentive in Report model enum', () => {
      const Report = require('../models/Report');
      const reportSchema = Report.schema;
      const typeField = reportSchema.paths.type;

      expect(typeField.enumValues).toContain('employmentTaxIncentive');
    });

    test('should validate ETI report type is supported', () => {
      const supportedReportTypes = [
        'employeeBasicInfo',
        'transactionHistory',
        'payrollVariance',
        'employmentTaxIncentive'
      ];

      expect(supportedReportTypes).toContain('employmentTaxIncentive');
    });
  });

  describe('Leave Reports Implementation', () => {
    test('should have all leave report types in Report model enum', () => {
      const Report = require('../models/Report');
      const reportSchema = Report.schema;
      const typeField = reportSchema.paths.type;

      // Test all three leave report types
      expect(typeField.enumValues).toContain('leaveReport');
      expect(typeField.enumValues).toContain('leaveDaysReport');
      expect(typeField.enumValues).toContain('leaveExpiryReport');
    });

    test('should validate all leave report types are supported', () => {
      const supportedLeaveReportTypes = [
        'leaveReport',
        'leaveDaysReport',
        'leaveExpiryReport'
      ];

      // Verify all leave report types are in our supported list
      supportedLeaveReportTypes.forEach(reportType => {
        expect(supportedLeaveReportTypes).toContain(reportType);
      });
    });

    test('should generate correct ETI filename with proper extension', () => {
      const getFileExtension = (format) => {
        switch (format) {
          case "pdf":
            return "pdf";
          case "excel":
            return "xlsx";
          case "csv":
            return "csv";
          default:
            return "txt";
        }
      };

      const reportType = 'employmentTaxIncentive';
      const formats = ['pdf', 'excel', 'csv'];
      const date = '2025-06-18';

      formats.forEach(format => {
        const filename = `${reportType}_${date}.${getFileExtension(format)}`;

        if (format === 'excel') {
          expect(filename).toBe('employmentTaxIncentive_2025-06-18.xlsx');
        } else {
          expect(filename).toBe(`employmentTaxIncentive_2025-06-18.${format}`);
        }

        expect(filename).not.toContain('.excel');
      });
    });
  });

  describe('Employee Basic Info Field Selection Fix', () => {
    test('should validate field selection functionality exists', () => {
      // Test that the field selection logic is properly implemented
      const mockSettings = {
        selectedFields: ['firstName', 'lastName', 'email', 'jobTitle']
      };

      // Verify selectedFields is properly handled
      expect(mockSettings.selectedFields).toBeInstanceOf(Array);
      expect(mockSettings.selectedFields.length).toBe(4);
      expect(mockSettings.selectedFields).toContain('firstName');
      expect(mockSettings.selectedFields).toContain('email');
    });

    test('should handle empty selectedFields gracefully', () => {
      const mockSettings = {
        selectedFields: []
      };

      // Should default to essential fields when no fields selected
      const defaultFields = mockSettings.selectedFields && mockSettings.selectedFields.length > 0
        ? mockSettings.selectedFields
        : ["companyEmployeeNumber", "firstName", "lastName"];

      expect(defaultFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
    });

    test('should handle undefined selectedFields gracefully', () => {
      const mockSettings = {};

      // Should default to essential fields when selectedFields is undefined
      const defaultFields = mockSettings.selectedFields && mockSettings.selectedFields.length > 0
        ? mockSettings.selectedFields
        : ["companyEmployeeNumber", "firstName", "lastName"];

      expect(defaultFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
    });

    test('should validate field mappings exist for common fields', () => {
      const commonFields = [
        'firstName', 'lastName', 'companyEmployeeNumber', 'email', 'phone',
        'jobTitle', 'department', 'dob', 'idNumber', 'bankName'
      ];

      // Mock field mappings (similar to what's in the actual code)
      const fieldMappings = {
        "firstName": { header: "First Name", key: "firstName" },
        "lastName": { header: "Last Name", key: "lastName" },
        "companyEmployeeNumber": { header: "Employee #", key: "companyEmployeeNumber" },
        "email": { header: "Email", key: "email" },
        "phone": { header: "Phone", key: "phone" },
        "jobTitle": { header: "Job Title", key: "jobTitle" },
        "department": { header: "Department", key: "department" },
        "dob": { header: "Date of Birth", key: "dob" },
        "idNumber": { header: "ID Number", key: "idNumber" },
        "bankName": { header: "Bank Name", key: "bankName" }
      };

      commonFields.forEach(field => {
        expect(fieldMappings[field]).toBeDefined();
        expect(fieldMappings[field].header).toBeTruthy();
        expect(fieldMappings[field].key).toBeTruthy();
      });
    });
  });

  describe('Null Settings Handling Fix', () => {
    test('should handle null settings gracefully', () => {
      const nullSettings = null;
      const safeSettings = nullSettings || {};

      // Should not throw error when accessing properties
      expect(() => {
        const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
          ? safeSettings.selectedFields
          : ["companyEmployeeNumber", "firstName", "lastName"];

        expect(selectedFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
      }).not.toThrow();
    });

    test('should handle undefined settings gracefully', () => {
      const undefinedSettings = undefined;
      const safeSettings = undefinedSettings || {};

      // Should not throw error when accessing properties
      expect(() => {
        const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
          ? safeSettings.selectedFields
          : ["companyEmployeeNumber", "firstName", "lastName"];

        expect(selectedFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
      }).not.toThrow();
    });

    test('should handle settings with null selectedFields', () => {
      const settingsWithNullFields = { selectedFields: null };
      const safeSettings = settingsWithNullFields || {};

      // Should not throw error and default to essential fields
      const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
        ? safeSettings.selectedFields
        : ["companyEmployeeNumber", "firstName", "lastName"];

      expect(selectedFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
    });

    test('should handle settings with empty selectedFields array', () => {
      const settingsWithEmptyFields = { selectedFields: [] };
      const safeSettings = settingsWithEmptyFields || {};

      // Should default to essential fields when array is empty
      const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
        ? safeSettings.selectedFields
        : ["companyEmployeeNumber", "firstName", "lastName"];

      expect(selectedFields).toEqual(["companyEmployeeNumber", "firstName", "lastName"]);
    });

    test('should preserve valid selectedFields when settings are valid', () => {
      const validSettings = {
        selectedFields: ['firstName', 'lastName', 'email', 'jobTitle']
      };
      const safeSettings = validSettings || {};

      // Should use the provided fields when valid
      const selectedFields = safeSettings.selectedFields && safeSettings.selectedFields.length > 0
        ? safeSettings.selectedFields
        : ["companyEmployeeNumber", "firstName", "lastName"];

      expect(selectedFields).toEqual(['firstName', 'lastName', 'email', 'jobTitle']);
    });
  });

  describe('Report Structure and Header Fix', () => {
    test('should validate Excel header structure logic', () => {
      const mockSelectedFields = ['firstName', 'lastName', 'email'];
      const mockFieldMappings = {
        "firstName": { header: "First Name", key: "firstName", width: 20 },
        "lastName": { header: "Last Name", key: "lastName", width: 20 },
        "email": { header: "Email", key: "email", width: 30 }
      };

      // Build columns array from selected fields (same logic as Excel generation)
      const columns = mockSelectedFields.map(field => {
        const mapping = mockFieldMappings[field];
        if (!mapping) {
          return { header: field, key: field, width: 15 };
        }
        return mapping;
      });

      // Verify column structure
      expect(columns).toHaveLength(3);
      expect(columns[0].header).toBe("First Name");
      expect(columns[1].header).toBe("Last Name");
      expect(columns[2].header).toBe("Email");

      // Verify header values extraction
      const headerValues = columns.map(col => col.header);
      expect(headerValues).toEqual(["First Name", "Last Name", "Email"]);
    });

    test('should validate field mapping consistency between Excel and CSV', () => {
      // Common fields that should have consistent mappings
      const commonFields = ['firstName', 'lastName', 'companyEmployeeNumber', 'email', 'jobTitle'];

      // Mock Excel field mappings
      const excelMappings = {
        "firstName": { header: "First Name", key: "firstName" },
        "lastName": { header: "Last Name", key: "lastName" },
        "companyEmployeeNumber": { header: "Employee #", key: "companyEmployeeNumber" },
        "email": { header: "Email", key: "email" },
        "jobTitle": { header: "Job Title", key: "jobTitle" }
      };

      // Mock CSV field mappings (should match Excel)
      const csvMappings = {
        "firstName": { header: "First Name", key: "firstName" },
        "lastName": { header: "Last Name", key: "lastName" },
        "companyEmployeeNumber": { header: "Employee #", key: "companyEmployeeNumber" },
        "email": { header: "Email", key: "email" },
        "jobTitle": { header: "Job Title", key: "jobTitle" }
      };

      // Verify consistency
      commonFields.forEach(field => {
        expect(excelMappings[field]).toBeDefined();
        expect(csvMappings[field]).toBeDefined();
        expect(excelMappings[field].header).toBe(csvMappings[field].header);
        expect(excelMappings[field].key).toBe(csvMappings[field].key);
      });
    });

    test('should validate comprehensive field coverage', () => {
      // All available fields that should be supported based on Employee model
      const allFields = [
        // Essential Information
        'companyEmployeeNumber', 'firstName', 'lastName', 'globalEmployeeId',

        // Personal Information
        'dob', 'gender', 'race', 'disabled', 'foreignNational', 'notRSACitizen',

        // Contact Information
        'email', 'phone', 'mobileNumber',

        // Employment Information
        'jobTitle', 'department', 'costCentre', 'workingHours', 'payFrequency', 'doa',
        'status', 'employmentStatus', 'isDirector', 'typeOfDirector', 'isContractor',

        // Identification Information
        'idType', 'idNumber', 'passportNumber', 'incomeTaxNumber',

        // Banking Information
        'paymentMethod', 'bank', 'accountType', 'accountNumber', 'branchCode',
        'accountHolder', 'holderRelationship',

        // Address Information
        'streetAddress', 'suburb', 'city', 'postalCode', 'province',
        'unitNumber', 'complex', 'streetNumber', 'street', 'code',
        'line1', 'line2', 'line3',

        // Employment Tax Incentive (ETI)
        'etiStatus', 'etiEffectiveFrom', 'isETIEligible', 'monthlyRemuneration',
        'ageAtEmployment', 'employmentStartDate',

        // Occupation Information
        'occupationLevel', 'occupationCategory', 'jobValue',

        // UIF Information
        'isUifExempt', 'uifExemptReason', 'uifStatusCode',

        // Working Hours Details
        'hoursPerDay',

        // Termination Information
        'terminationNoticeDate', 'lastDayOfService', 'rehireEligibility',
        'exitInterviewDate', 'lastPayrollDate',

        // OID Information
        'oidEligible', 'oidExemptReason',

        // Self Service
        'selfServiceEnabled', 'lastActivity', 'essEmailSent'
      ];

      // Mock comprehensive field mappings (should match our implementation)
      const comprehensiveMappings = {};
      allFields.forEach(field => {
        comprehensiveMappings[field] = {
          header: field.charAt(0).toUpperCase() + field.slice(1),
          key: field
        };
      });

      // Verify all fields have mappings
      allFields.forEach(field => {
        expect(comprehensiveMappings[field]).toBeDefined();
        expect(comprehensiveMappings[field].header).toBeTruthy();
        expect(comprehensiveMappings[field].key).toBeTruthy();
      });

      console.log(`✅ Verified ${allFields.length} comprehensive field mappings`);
    });
  });

  describe('Company Name and Field Selection Fixes', () => {
    test('should handle company name metadata correctly', () => {
      // Mock company and employer details
      const mockCompany = { _id: 'company123', name: 'Test Company Ltd' };
      const mockEmployerDetails = { tradingName: 'Test Trading Name' };

      // Test company name selection logic
      const companyName1 = mockEmployerDetails?.tradingName || mockCompany.name;
      expect(companyName1).toBe('Test Trading Name');

      // Test fallback when no trading name
      const mockEmployerDetailsNoTrading = {};
      const companyName2 = mockEmployerDetailsNoTrading?.tradingName || mockCompany.name;
      expect(companyName2).toBe('Test Company Ltd');

      // Test fallback when no employer details
      const companyName3 = undefined?.tradingName || mockCompany.name;
      expect(companyName3).toBe('Test Company Ltd');
    });

    test('should handle settings.toObject() safely', () => {
      // Test with valid settings
      const validSettings = {
        toObject: () => ({ someProperty: 'value' }),
        selectedFields: ['firstName', 'lastName']
      };

      const settingsWithFields1 = {
        ...(validSettings ? validSettings.toObject() : {}),
        selectedFields: ['firstName', 'lastName', 'email']
      };

      expect(settingsWithFields1.someProperty).toBe('value');
      expect(settingsWithFields1.selectedFields).toEqual(['firstName', 'lastName', 'email']);

      // Test with null settings
      const nullSettings = null;
      const settingsWithFields2 = {
        ...(nullSettings ? nullSettings.toObject() : {}),
        selectedFields: ['firstName', 'lastName']
      };

      expect(settingsWithFields2.selectedFields).toEqual(['firstName', 'lastName']);
      expect(Object.keys(settingsWithFields2)).toEqual(['selectedFields']);
    });

    test('should validate field processing logic for all data types', () => {
      // Mock employee with various field types
      const mockEmployee = {
        firstName: 'John',
        lastName: 'Doe',
        dob: new Date('1990-01-01'),
        employmentStatus: true,
        isDirector: false,
        payFrequency: { name: 'Monthly' },
        bank: 'Test Bank',
        mobileNumber: '**********',
        postalCode: '12345',
        undefinedField: undefined,
        nullField: null,
        emptyField: ''
      };

      // Test field processing logic
      const testFields = ['firstName', 'dob', 'employmentStatus', 'isDirector', 'payFrequency', 'bank', 'undefinedField'];

      testFields.forEach(field => {
        let value = '';

        if (mockEmployee[field] !== undefined && mockEmployee[field] !== null) {
          switch (field) {
            case 'dob':
              value = mockEmployee[field] ? new Date(mockEmployee[field]).toLocaleDateString() : '';
              break;
            case 'employmentStatus':
            case 'isDirector':
              value = mockEmployee[field] ? 'Yes' : 'No';
              break;
            case 'payFrequency':
              value = mockEmployee.payFrequency?.name || '';
              break;
            default:
              value = mockEmployee[field] || '';
          }
        }

        // Verify expected values
        switch (field) {
          case 'firstName':
            expect(value).toBe('John');
            break;
          case 'dob':
            expect(value).toBe(new Date('1990-01-01').toLocaleDateString());
            break;
          case 'employmentStatus':
            expect(value).toBe('Yes');
            break;
          case 'isDirector':
            expect(value).toBe('No');
            break;
          case 'payFrequency':
            expect(value).toBe('Monthly');
            break;
          case 'bank':
            expect(value).toBe('Test Bank');
            break;
          case 'undefinedField':
            expect(value).toBe('');
            break;
        }
      });
    });
  });

  describe('Payslips Report Generation', () => {
    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
    });

    test('should generate payslips report with correct aggregation query', async () => {
      // Mock data
      const mockCompanyId = new mongoose.Types.ObjectId();
      const mockCompany = {
        _id: mockCompanyId,
        name: 'Test Company',
        tradingName: 'Test Trading Name',
        companyCode: 'TEST001'
      };
      const mockEmployerDetails = {
        physicalAddress: '123 Test Street',
        postalAddress: 'PO Box 123'
      };
      const mockPayslipData = [
        {
          _id: new mongoose.Types.ObjectId(),
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-01-31'),
          basicSalary: 15000,
          grossPay: 15000,
          totalDeductions: 3000,
          netPay: 12000,
          PAYE: 2000,
          UIF: 150,
          SDL: 225,
          employee: {
            id: new mongoose.Types.ObjectId(),
            companyEmployeeNumber: 'EMP001',
            firstName: 'John',
            lastName: 'Doe',
            idNumber: '8001015555088',
            email: '<EMAIL>',
            department: 'IT',
            paymentMethod: 'EFT'
          },
          company: {
            name: 'Test Company',
            tradingName: 'Test Trading Name'
          },
          payFrequency: {
            frequency: 'monthly',
            description: 'Monthly Month End'
          }
        }
      ];

      // Mock the database calls
      Company.findById.mockResolvedValue(mockCompany);
      EmployerDetails.findOne.mockResolvedValue(mockEmployerDetails);
      PayrollPeriod.aggregate.mockResolvedValue(mockPayslipData);

      // Test parameters
      const format = 'excel';
      const settings = {};
      const dateRange = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      };

      // Call the method
      const result = await ReportController.generatePayslips(
        mockCompanyId,
        format,
        settings,
        dateRange
      );

      // Verify the aggregation was called with correct parameters
      expect(PayrollPeriod.aggregate).toHaveBeenCalledWith([
        {
          $match: {
            company: mockCompanyId,
            isFinalized: true,
            endDate: {
              $gte: new Date('2024-01-01'),
              $lte: new Date('2024-01-31')
            }
          }
        },
        expect.objectContaining({
          $lookup: expect.objectContaining({
            from: "employees",
            localField: "employee",
            foreignField: "_id",
            as: "employeeData"
          })
        }),
        expect.any(Object), // $unwind
        expect.any(Object), // employee status filter
        expect.any(Object), // company lookup
        expect.any(Object), // company unwind
        expect.any(Object), // pay frequency lookup
        expect.any(Object), // pay frequency unwind
        expect.any(Object), // projection
        expect.any(Object)  // sort
      ]);

      // Verify the result is a buffer (Excel file)
      expect(result).toBeInstanceOf(Buffer);
    });

    test('should handle employee filtering correctly', async () => {
      const mockCompanyId = new mongoose.Types.ObjectId();
      const selectedEmployees = [new mongoose.Types.ObjectId(), new mongoose.Types.ObjectId()];

      Company.findById.mockResolvedValue({ _id: mockCompanyId, name: 'Test' });
      EmployerDetails.findOne.mockResolvedValue({});
      PayrollPeriod.aggregate.mockResolvedValue([]);

      await ReportController.generatePayslips(
        mockCompanyId,
        'excel',
        {},
        null,
        selectedEmployees
      );

      // Verify that employee filter was applied
      const aggregateCall = PayrollPeriod.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall[0].$match;

      expect(matchStage.employee).toEqual({
        $in: selectedEmployees.map(id => new mongoose.Types.ObjectId(id))
      });
    });

    test('should handle missing date range gracefully', async () => {
      const mockCompanyId = new mongoose.Types.ObjectId();

      Company.findById.mockResolvedValue({ _id: mockCompanyId, name: 'Test' });
      EmployerDetails.findOne.mockResolvedValue({});
      PayrollPeriod.aggregate.mockResolvedValue([]);

      await ReportController.generatePayslips(
        mockCompanyId,
        'excel',
        {},
        null // no date range
      );

      // Verify that no date filter was applied
      const aggregateCall = PayrollPeriod.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall[0].$match;

      expect(matchStage.endDate).toBeUndefined();
    });
  });
});
