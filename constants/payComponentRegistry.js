/**
 * PandaPayroll Pay Component Registry
 * 
 * This is a READ-ONLY registry that documents the current pay component structure
 * without modifying any existing functionality. Used for enhanced display logic only.
 * 
 * IMPORTANT: This file does NOT change any calculations or data storage.
 * All existing tax compliance features (PAYE, UIF, SDL) remain unchanged.
 */

const PAY_COMPONENT_REGISTRY = {
  // ===== INCOME COMPONENTS =====
  INCOME: {
    category: 'Income',
    icon: 'ph-money',
    color: '#10b981', // Green
    components: {
      basicSalary: {
        name: 'Basic Salary',
        description: 'Employee base salary',
        taxable: true,
        required: true,
        displayOrder: 1,
        calculation: 'direct', // Direct value from database
        source: 'basicSalary'
      },
      commission: {
        name: 'Commission',
        description: 'Sales commission payments',
        taxable: true,
        required: false,
        displayOrder: 2,
        calculation: 'direct',
        source: 'commission'
      },
      lossOfIncome: {
        name: 'Loss of Income',
        description: 'Insurance payout for loss of income',
        taxable: false, // Non-taxable benefit
        required: false,
        displayOrder: 3,
        calculation: 'direct',
        source: 'lossOfIncome'
      },
      overtime: {
        name: 'Overtime',
        description: 'Overtime payments',
        taxable: true,
        required: false,
        displayOrder: 4,
        calculation: 'computed', // Needs calculation
        source: 'overtime'
      }
    }
  },

  // ===== ALLOWANCE COMPONENTS =====
  ALLOWANCES: {
    category: 'Allowances',
    icon: 'ph-car',
    color: '#3b82f6', // Blue
    components: {
      travelAllowance: {
        name: 'Travel Allowance',
        description: 'Monthly travel allowance',
        taxable: 'partial', // 80% taxable, 20% non-taxable
        required: false,
        displayOrder: 1,
        calculation: 'complex',
        source: 'travelAllowance',
        breakdown: {
          total: 'travelAllowance.fixedAllowanceAmount',
          taxable: 'calculated', // 80% of total
          nonTaxable: 'calculated' // 20% of total
        }
      },
      phoneAllowance: {
        name: 'Phone Allowance',
        description: 'Monthly phone allowance',
        taxable: true,
        required: false,
        displayOrder: 2,
        calculation: 'direct',
        source: 'phoneAllowance'
      },
      computerAllowance: {
        name: 'Computer Allowance',
        description: 'Computer/IT allowance',
        taxable: true,
        required: false,
        displayOrder: 3,
        calculation: 'direct',
        source: 'computerAllowance'
      }
    }
  },

  // ===== BENEFIT COMPONENTS =====
  BENEFITS: {
    category: 'Benefits',
    icon: 'ph-heart',
    color: '#8b5cf6', // Purple
    components: {
      accommodationBenefit: {
        name: 'Accommodation Benefit',
        description: 'Company-provided accommodation',
        taxable: true,
        required: false,
        displayOrder: 1,
        calculation: 'direct',
        source: 'accommodationBenefit'
      },
      companyCar: {
        name: 'Company Car',
        description: 'Company car benefit',
        taxable: true,
        required: false,
        displayOrder: 2,
        calculation: 'complex',
        source: 'companyCar',
        breakdown: {
          benefit: 'calculated',
          maintenance: 'companyCar.maintenance',
          fuel: 'companyCar.fuel'
        }
      },
      medicalAid: {
        name: 'Medical Aid',
        description: 'Medical aid contributions and benefits',
        taxable: 'complex', // Employee contribution = deduction, Employer contribution = benefit
        required: false,
        displayOrder: 3,
        calculation: 'complex',
        source: 'medicalAid',
        breakdown: {
          employeeContribution: 'medicalAid.employeeContribution',
          employerContribution: 'medicalAid.employerContribution',
          taxCredit: 'calculated',
          members: 'medicalAid.members'
        }
      }
    }
  },

  // ===== DEDUCTION COMPONENTS =====
  DEDUCTIONS: {
    category: 'Deductions',
    icon: 'ph-minus-circle',
    color: '#ef4444', // Red
    components: {
      paye: {
        name: 'PAYE Tax',
        description: 'Pay As You Earn income tax',
        taxable: false, // This IS the tax
        required: true,
        displayOrder: 1,
        calculation: 'computed',
        source: 'calculated.paye'
      },
      uif: {
        name: 'UIF',
        description: 'Unemployment Insurance Fund',
        taxable: false,
        required: true,
        displayOrder: 2,
        calculation: 'computed',
        source: 'calculated.uif'
      },
      sdl: {
        name: 'SDL',
        description: 'Skills Development Levy',
        taxable: false,
        required: false,
        displayOrder: 3,
        calculation: 'computed',
        source: 'calculated.sdl'
      },
      medicalAidEmployee: {
        name: 'Medical Aid (Employee)',
        description: 'Employee medical aid contribution',
        taxable: false, // Deduction reduces taxable income
        required: false,
        displayOrder: 4,
        calculation: 'direct',
        source: 'medicalAid.employeeContribution'
      },
      pensionFund: {
        name: 'Pension Fund',
        description: 'Pension fund contribution',
        taxable: false,
        required: false,
        displayOrder: 5,
        calculation: 'direct',
        source: 'pensionFund.employeeContribution'
      },
      garnishee: {
        name: 'Garnishee Order',
        description: 'Court-ordered deduction',
        taxable: false,
        required: false,
        displayOrder: 6,
        calculation: 'direct',
        source: 'garnishee'
      },
      maintenanceOrder: {
        name: 'Maintenance Order',
        description: 'Maintenance payment order',
        taxable: false,
        required: false,
        displayOrder: 7,
        calculation: 'direct',
        source: 'maintenanceOrder'
      }
    }
  }
};

/**
 * Helper function to get all components for a category
 * @param {string} category - Category name (INCOME, ALLOWANCES, BENEFITS, DEDUCTIONS)
 * @returns {Object} Category configuration with components
 */
function getComponentsByCategory(category) {
  return PAY_COMPONENT_REGISTRY[category] || null;
}

/**
 * Helper function to get a specific component configuration
 * @param {string} category - Category name
 * @param {string} componentKey - Component key
 * @returns {Object} Component configuration
 */
function getComponent(category, componentKey) {
  const categoryConfig = PAY_COMPONENT_REGISTRY[category];
  if (!categoryConfig) return null;
  
  return categoryConfig.components[componentKey] || null;
}

/**
 * Helper function to get all components sorted by display order
 * @param {string} category - Category name
 * @returns {Array} Array of component configurations with keys
 */
function getSortedComponents(category) {
  const categoryConfig = PAY_COMPONENT_REGISTRY[category];
  if (!categoryConfig) return [];
  
  return Object.entries(categoryConfig.components)
    .map(([key, config]) => ({ key, ...config }))
    .sort((a, b) => a.displayOrder - b.displayOrder);
}

/**
 * Helper function to check if a component has a value in the payroll data
 * @param {Object} payrollData - Payroll data object
 * @param {string} source - Source path (e.g., 'payroll.basicSalary')
 * @returns {boolean} True if component has a non-zero value
 */
function hasValue(payrollData, source) {
  if (!payrollData || !source) return false;
  
  // Handle nested object paths
  const parts = source.split('.');
  let value = payrollData;
  
  for (const part of parts) {
    if (value && typeof value === 'object' && part in value) {
      value = value[part];
    } else {
      return false;
    }
  }
  
  return value !== null && value !== undefined && value !== 0 && value !== '';
}

module.exports = {
  PAY_COMPONENT_REGISTRY,
  getComponentsByCategory,
  getComponent,
  getSortedComponents,
  hasValue
};
