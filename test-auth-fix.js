#!/usr/bin/env node

/**
 * Test script to verify authentication fixes
 * This script tests the JWT-to-session integration
 */

const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Import models
const User = require('./models/user');
const Company = require('./models/Company');

async function testAuthenticationFix() {
  try {
    console.log('🔧 Testing Authentication Fix...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✓ Connected to MongoDB');
    
    // Find a test user
    const testUser = await User.findOne().populate('currentCompany');
    if (!testUser) {
      console.log('✗ No users found in database');
      return;
    }
    
    console.log('✓ Found test user:', testUser.email);
    
    // Generate a JWT token for the user
    const tokenPayload = {
      id: testUser._id,
      username: testUser.username || testUser.email,
      email: testUser.email,
      role: testUser.role || 'user'
    };
    
    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: '24h' });
    console.log('✓ Generated JWT token');
    
    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('✓ JWT token verified successfully');
    console.log('  - User ID:', decoded.id);
    console.log('  - Email:', decoded.email);
    console.log('  - Role:', decoded.role);
    
    // Test user lookup
    const userFromToken = await User.findById(decoded.id).populate('currentCompany');
    if (userFromToken) {
      console.log('✓ User found from JWT token');
      console.log('  - Has company:', !!userFromToken.currentCompany);
      if (userFromToken.currentCompany) {
        console.log('  - Company code:', userFromToken.currentCompany.companyCode);
      }
    } else {
      console.log('✗ User not found from JWT token');
    }
    
    console.log('\n🎉 Authentication fix test completed successfully!');
    console.log('\nKey improvements made:');
    console.log('1. ✓ Enhanced ensureAuthenticated to check both session AND JWT');
    console.log('2. ✓ Improved JWT middleware to create sessions for valid tokens');
    console.log('3. ✓ Enhanced session store configuration for production');
    console.log('4. ✓ Added JWT-to-session bridging middleware');
    console.log('5. ✓ Improved Passport.js deserializeUser error handling');
    
  } catch (error) {
    console.error('✗ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n✓ Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  testAuthenticationFix().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Test script error:', error);
    process.exit(1);
  });
}

module.exports = { testAuthenticationFix };
