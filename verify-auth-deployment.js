#!/usr/bin/env node

/**
 * Deployment verification script for authentication fixes
 * This script verifies that all authentication components are working
 */

const fs = require('fs');
const path = require('path');

function verifyAuthenticationDeployment() {
  console.log('🔍 Verifying Authentication Deployment...\n');
  
  const checks = [];
  
  // Check 1: Enhanced ensureAuthenticated middleware
  try {
    const authConfigPath = path.join(__dirname, 'config/auth.js');
    const authConfig = fs.readFileSync(authConfigPath, 'utf8');
    
    if (authConfig.includes('Enhanced Authentication Check') && 
        authConfig.includes('req.jwtUser') &&
        authConfig.includes('JWT authentication found')) {
      checks.push('✓ Enhanced ensureAuthenticated middleware deployed');
    } else {
      checks.push('✗ Enhanced ensureAuthenticated middleware missing');
    }
  } catch (error) {
    checks.push('✗ Could not verify ensureAuthenticated middleware');
  }
  
  // Check 2: Improved JWT middleware
  try {
    const jwtUtilsPath = path.join(__dirname, 'utils/jwtUtils.js');
    const jwtUtils = fs.readFileSync(jwtUtilsPath, 'utf8');
    
    if (jwtUtils.includes('Creating session for JWT user') && 
        jwtUtils.includes('Invalid JWT token detected')) {
      checks.push('✓ Improved JWT middleware deployed');
    } else {
      checks.push('✗ Improved JWT middleware missing');
    }
  } catch (error) {
    checks.push('✗ Could not verify JWT middleware');
  }
  
  // Check 3: Enhanced session configuration
  try {
    const appPath = path.join(__dirname, 'app.js');
    const appContent = fs.readFileSync(appPath, 'utf8');
    
    if (appContent.includes('Enhanced authentication state logging') && 
        appContent.includes('bridging JWT to session')) {
      checks.push('✓ Enhanced session configuration deployed');
    } else {
      checks.push('✗ Enhanced session configuration missing');
    }
  } catch (error) {
    checks.push('✗ Could not verify session configuration');
  }
  
  // Check 4: Improved Passport configuration
  try {
    const passportPath = path.join(__dirname, 'config/passport.js');
    const passportContent = fs.readFileSync(passportPath, 'utf8');
    
    if (passportContent.includes('Deserializing user') && 
        passportContent.includes('User deserialized successfully')) {
      checks.push('✓ Improved Passport configuration deployed');
    } else {
      checks.push('✗ Improved Passport configuration missing');
    }
  } catch (error) {
    checks.push('✗ Could not verify Passport configuration');
  }
  
  // Check 5: Environment variables
  require('dotenv').config();
  if (process.env.JWT_SECRET && process.env.SESSION_SECRET && process.env.MONGODB_URI) {
    checks.push('✓ Required environment variables present');
  } else {
    checks.push('✗ Missing required environment variables');
  }
  
  // Display results
  console.log('Deployment Verification Results:');
  console.log('================================');
  checks.forEach(check => console.log(check));
  
  const passedChecks = checks.filter(check => check.startsWith('✓')).length;
  const totalChecks = checks.length;
  
  console.log(`\nSummary: ${passedChecks}/${totalChecks} checks passed`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 All authentication fixes deployed successfully!');
    console.log('\nNext steps:');
    console.log('1. Deploy to production environment');
    console.log('2. Monitor authentication logs');
    console.log('3. Test with actual user sessions');
    console.log('4. Verify JWT-to-session bridging works');
    return true;
  } else {
    console.log('\n⚠️  Some authentication fixes are missing or incomplete');
    console.log('Please review the failed checks above');
    return false;
  }
}

// Run verification
if (require.main === module) {
  const success = verifyAuthenticationDeployment();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyAuthenticationDeployment };
