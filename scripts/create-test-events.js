#!/usr/bin/env node

/**
 * Test Event Creation Script for PandaPayroll Email Reminders
 * 
 * This script creates test events with specific dates and reminder settings
 * to help test the email reminder functionality.
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
const User = require('../models/user');
const Company = require('../models/Company');

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function createTestEvents() {
  console.log('\n🧪 Creating Test Events for Email Reminder Testing');
  console.log('='.repeat(55));

  // Get the first company and user for testing
  const company = await Company.findOne();
  if (!company) {
    console.log('❌ No company found. Please ensure you have at least one company in the database.');
    return;
  }

  let user = await User.findOne({
    $or: [
      { companies: company._id },
      { currentCompany: company._id },
      { defaultCompany: company._id }
    ]
  });

  if (!user) {
    // Try to find any user and add them to the company
    const anyUser = await User.findOne();
    if (!anyUser) {
      console.log('❌ No user found. Please ensure you have at least one user in the database.');
      return;
    }
    console.log(`📝 Using user: ${anyUser.email} and associating with company: ${company.name}`);
    user = anyUser;
  }

  console.log(`🏢 Using Company: ${company.name} (${company._id})`);
  console.log(`👤 Using User: ${user.email} (${user._id})`);

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Create test events with different reminder schedules
  const testEvents = [
    {
      title: 'TEST: EMP201 Due Tomorrow',
      description: 'Test event to trigger 1-day reminder',
      date: new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000), // Tomorrow
      type: 'emp201',
      category: 'tax_compliance',
      priority: 'high',
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [7, 3, 1],
        emailReminder: true,
        dashboardReminder: true
      }
    },
    {
      title: 'TEST: IRP5 Due in 3 Days',
      description: 'Test event to trigger 3-day reminder',
      date: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      type: 'irp5',
      category: 'tax_compliance',
      priority: 'critical',
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [7, 3, 1],
        emailReminder: true,
        dashboardReminder: true
      }
    },
    {
      title: 'TEST: SDL Due in 7 Days',
      description: 'Test event to trigger 7-day reminder',
      date: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      type: 'sdl',
      category: 'statutory',
      priority: 'medium',
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [14, 7, 3],
        emailReminder: true,
        dashboardReminder: true
      }
    },
    {
      title: 'TEST: UIF Due in 14 Days',
      description: 'Test event to trigger 14-day reminder',
      date: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      type: 'uif',
      category: 'statutory',
      priority: 'medium',
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [14, 7, 3],
        emailReminder: true,
        dashboardReminder: true
      }
    },
    {
      title: 'TEST: Custom Event Due Today',
      description: 'Test event to trigger same-day reminder',
      date: today, // Today
      type: 'custom',
      category: 'custom', // Use valid enum value
      priority: 'low',
      reminderSettings: {
        enabled: true,
        daysBeforeEvent: [0, 1, 3], // Include 0 for same-day reminders
        emailReminder: true,
        dashboardReminder: true
      }
    }
  ];

  console.log('\n📅 Creating Test Events:');
  console.log('-'.repeat(25));

  for (const eventData of testEvents) {
    try {
      // Check if similar test event already exists
      const existingEvent = await PayrollCalendarEvent.findOne({
        title: eventData.title,
        company: company._id
      });

      if (existingEvent) {
        console.log(`⚠️  Event already exists: ${eventData.title}`);
        continue;
      }

      const event = new PayrollCalendarEvent({
        ...eventData,
        company: company._id,
        createdBy: user._id,
        assignedTo: [user._id],
        status: 'pending',
        isRecurring: false,
        metadata: {
          source: 'manual', // Use valid enum value
          tags: ['test', 'email_reminder_testing']
        }
      });

      await event.save();
      
      const daysUntil = Math.ceil((event.date - today) / (1000 * 60 * 60 * 24));
      console.log(`✅ Created: ${event.title}`);
      console.log(`   📅 Date: ${event.date.toISOString().split('T')[0]} (${daysUntil} days from now)`);
      console.log(`   🔔 Reminder Days: [${event.reminderSettings.daysBeforeEvent.join(', ')}]`);
      console.log(`   📧 Should trigger reminder: ${event.reminderSettings.daysBeforeEvent.includes(daysUntil) ? 'YES' : 'NO'}`);
      
    } catch (error) {
      console.error(`❌ Failed to create event: ${eventData.title}`, error.message);
    }
  }

  console.log('\n📊 Summary:');
  const totalTestEvents = await PayrollCalendarEvent.countDocuments({
    'metadata.tags': 'test'
  });
  console.log(`Total Test Events Created: ${totalTestEvents}`);

  console.log('\n🔍 Next Steps:');
  console.log('1. Run the diagnostic script: node scripts/diagnose-email-reminders.js');
  console.log('2. Test email reminders by clicking "Send Email Reminders" in the dashboard');
  console.log('3. Check server logs for detailed email sending information');
  console.log('4. Verify emails are received at the configured email addresses');
}

async function cleanupTestEvents() {
  console.log('\n🧹 Cleaning up test events...');
  
  const result = await PayrollCalendarEvent.deleteMany({
    'metadata.tags': 'test'
  });
  
  console.log(`✅ Deleted ${result.deletedCount} test events`);
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    await connectToDatabase();
    
    if (command === 'cleanup') {
      await cleanupTestEvents();
    } else {
      await createTestEvents();
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

if (require.main === module) {
  main();
}

module.exports = { createTestEvents, cleanupTestEvents };
