#!/usr/bin/env node

/**
 * Dashboard Reminder Functionality Test Script
 * 
 * This script tests the three specific issues:
 * 1. Manual Email Reminders Not Working
 * 2. "Reminder Status" Button Non-Functional  
 * 3. "Reminders" Dropdown Button Issues
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function testDashboardReminders() {
  console.log('🧪 Testing PandaPayroll Dashboard Reminder Functionality');
  console.log('='.repeat(60));

  let browser;
  let page;

  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Set to true for headless testing
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    page = await browser.newPage();

    // Enable console logging from the page
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'error') {
        console.log(`🔴 Browser Console Error: ${text}`);
      } else if (type === 'warn') {
        console.log(`🟡 Browser Console Warning: ${text}`);
      } else if (text.includes('reminder') || text.includes('email')) {
        console.log(`📝 Browser Console: ${text}`);
      }
    });

    // Listen for network requests
    page.on('requestfailed', request => {
      console.log(`❌ Network Request Failed: ${request.url()} - ${request.failure().errorText}`);
    });

    page.on('response', response => {
      const url = response.url();
      if (url.includes('reminder') || url.includes('send-reminders')) {
        console.log(`📡 Network Response: ${response.status()} ${url}`);
      }
    });

    // Navigate to dashboard
    console.log('\n1️⃣ Navigating to dashboard...');
    await page.goto('http://localhost:3002/dashboard', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // Wait for page to load
    await page.waitForTimeout(2000);

    console.log('✅ Dashboard loaded successfully');

    // Test Issue 3: Check if Reminders dropdown exists and works
    console.log('\n3️⃣ Testing Reminders Dropdown Button...');
    
    const reminderDropdown = await page.$('button[data-bs-toggle="dropdown"]:has-text("Reminders")');
    if (!reminderDropdown) {
      // Try alternative selector
      const reminderDropdownAlt = await page.$('.dropdown button:has(.ph-envelope)');
      if (reminderDropdownAlt) {
        console.log('✅ Found Reminders dropdown button (alternative selector)');
        await reminderDropdownAlt.click();
        await page.waitForTimeout(500);
        
        const dropdownMenu = await page.$('.dropdown-menu');
        if (dropdownMenu) {
          console.log('✅ Dropdown menu opens successfully');
        } else {
          console.log('❌ Dropdown menu does not appear');
        }
      } else {
        console.log('❌ Reminders dropdown button not found');
        return;
      }
    } else {
      console.log('✅ Found Reminders dropdown button');
      await reminderDropdown.click();
      await page.waitForTimeout(500);
    }

    // Test Issue 2: Check Reminder Status button
    console.log('\n2️⃣ Testing Reminder Status Button...');
    
    const reminderStatusBtn = await page.$('#reminderStatusBtn');
    if (reminderStatusBtn) {
      console.log('✅ Found Reminder Status button');
      
      // Click the button and check for modal
      await reminderStatusBtn.click();
      await page.waitForTimeout(2000);
      
      const modal = await page.$('#reminderStatusModal');
      if (modal) {
        console.log('✅ Reminder Status modal appears');
        
        // Close modal
        const closeBtn = await page.$('#reminderStatusModal .modal-close');
        if (closeBtn) {
          await closeBtn.click();
          await page.waitForTimeout(500);
        }
      } else {
        console.log('❌ Reminder Status modal does not appear');
        
        // Check for JavaScript errors
        const errors = await page.evaluate(() => {
          return window.lastError || 'No JavaScript errors captured';
        });
        console.log(`🔍 JavaScript Error Check: ${errors}`);
      }
    } else {
      console.log('❌ Reminder Status button not found');
    }

    // Test Issue 1: Check Send Email Reminders button
    console.log('\n1️⃣ Testing Send Email Reminders Button...');
    
    // First open dropdown again if needed
    const dropdownMenu = await page.$('.dropdown-menu');
    if (!dropdownMenu || !(await dropdownMenu.isVisible())) {
      const reminderDropdownBtn = await page.$('.dropdown button:has(.ph-envelope)');
      if (reminderDropdownBtn) {
        await reminderDropdownBtn.click();
        await page.waitForTimeout(500);
      }
    }
    
    const sendRemindersBtn = await page.$('#sendRemindersBtn');
    if (sendRemindersBtn) {
      console.log('✅ Found Send Email Reminders button');
      
      // Click the button and check for confirmation toast
      await sendRemindersBtn.click();
      await page.waitForTimeout(1000);
      
      // Check for confirmation toast
      const confirmationToast = await page.$('#toast-container');
      if (confirmationToast) {
        console.log('✅ Confirmation toast appears');
        
        // Look for confirm button in toast
        const confirmBtn = await page.$('#toast-container .toast-action-confirm');
        if (confirmBtn) {
          console.log('✅ Found confirm button in toast');
          
          // Click confirm and monitor network requests
          console.log('📡 Clicking confirm - monitoring network requests...');
          await confirmBtn.click();
          
          // Wait for API call to complete
          await page.waitForTimeout(5000);
          
          // Check for success notification
          const successNotification = await page.$('.toast-success');
          if (successNotification) {
            console.log('✅ Success notification appears');
            
            const notificationText = await page.evaluate(el => el.textContent, successNotification);
            console.log(`📧 Notification: ${notificationText}`);
          } else {
            console.log('❌ No success notification appears');
          }
        } else {
          console.log('❌ Confirm button not found in toast');
        }
      } else {
        console.log('❌ Confirmation toast does not appear');
      }
    } else {
      console.log('❌ Send Email Reminders button not found');
    }

    // Additional debugging: Check for JavaScript functions
    console.log('\n🔍 Debugging JavaScript Functions...');
    
    const functionsExist = await page.evaluate(() => {
      return {
        sendEmailReminders: typeof sendEmailReminders !== 'undefined',
        showReminderStatus: typeof showReminderStatus !== 'undefined',
        showConfirmationToast: typeof showConfirmationToast !== 'undefined',
        Notifications: typeof Notifications !== 'undefined',
        attachEventListeners: typeof attachEventListeners !== 'undefined'
      };
    });
    
    console.log('JavaScript Functions Status:');
    Object.entries(functionsExist).forEach(([func, exists]) => {
      console.log(`  ${exists ? '✅' : '❌'} ${func}: ${exists ? 'Available' : 'Missing'}`);
    });

    // Check for event listeners
    const eventListeners = await page.evaluate(() => {
      const sendBtn = document.getElementById('sendRemindersBtn');
      const statusBtn = document.getElementById('reminderStatusBtn');
      
      return {
        sendBtnHasListeners: sendBtn ? sendBtn.onclick !== null || sendBtn.addEventListener.length > 0 : false,
        statusBtnHasListeners: statusBtn ? statusBtn.onclick !== null || statusBtn.addEventListener.length > 0 : false
      };
    });
    
    console.log('Event Listeners Status:');
    Object.entries(eventListeners).forEach(([listener, exists]) => {
      console.log(`  ${exists ? '✅' : '❌'} ${listener}: ${exists ? 'Attached' : 'Missing'}`);
    });

    console.log('\n✅ Dashboard Reminder Testing Complete');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
async function checkPuppeteer() {
  try {
    require('puppeteer');
    return true;
  } catch (error) {
    console.log('❌ Puppeteer not available. Installing...');
    console.log('Run: npm install puppeteer');
    return false;
  }
}

async function manualAPITest() {
  console.log('\n🔧 Manual API Testing (without browser)');
  console.log('='.repeat(40));

  const fetch = require('node-fetch');

  try {
    // Test reminder status endpoint
    console.log('Testing /api/payroll-calendar/reminder-status...');
    const statusResponse = await fetch('http://localhost:3002/api/payroll-calendar/reminder-status');
    console.log(`Status Response: ${statusResponse.status} ${statusResponse.statusText}`);
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      console.log('✅ Reminder Status API working');
      console.log(`Events found: ${statusData.data?.length || 0}`);
    } else {
      console.log('❌ Reminder Status API failed');
    }

    // Test send reminders endpoint (this requires authentication)
    console.log('\nTesting /api/payroll-calendar/send-reminders...');
    const sendResponse = await fetch('http://localhost:3002/api/payroll-calendar/send-reminders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    console.log(`Send Response: ${sendResponse.status} ${sendResponse.statusText}`);
    
    if (sendResponse.status === 401 || sendResponse.status === 403) {
      console.log('⚠️  Authentication required (expected for manual test)');
    } else if (sendResponse.ok) {
      console.log('✅ Send Reminders API accessible');
    } else {
      console.log('❌ Send Reminders API failed');
    }

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

async function main() {
  const hasPuppeteer = await checkPuppeteer();
  
  if (hasPuppeteer) {
    await testDashboardReminders();
  } else {
    console.log('Skipping browser tests - Puppeteer not available');
  }
  
  await manualAPITest();
}

if (require.main === module) {
  main();
}

module.exports = { testDashboardReminders, manualAPITest };
