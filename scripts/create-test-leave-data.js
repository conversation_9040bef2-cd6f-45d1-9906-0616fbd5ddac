const mongoose = require("mongoose");
const Company = require("../models/company");
const Employee = require("../models/Employee");
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const LeaveRequest = require("../models/LeaveRequest");
const User = require("../models/User");

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestLeaveData() {
  try {
    console.log("🚀 Creating test leave data for reporting...");

    // Find the test company
    const company = await Company.findOne({ companyCode: "CEA08B3B" });
    if (!company) {
      console.error("❌ Test company not found");
      return;
    }
    console.log(`✅ Using company: ${company.name}`);

    // Find a test user for createdBy fields
    const user = await User.findOne({ companies: company._id });
    if (!user) {
      console.error("❌ No user found for this company");
      return;
    }
    console.log(`✅ Using user: ${user.firstName} ${user.lastName}`);

    // Find employees
    const employees = await Employee.find({ company: company._id, status: "Active" }).limit(5);
    if (employees.length === 0) {
      console.error("❌ No active employees found");
      return;
    }
    console.log(`✅ Found ${employees.length} employees`);

    // Create leave types if they don't exist
    const leaveTypesData = [
      {
        name: "Annual Leave",
        category: "annual",
        description: "Annual vacation leave",
        daysPerYear: 21,
        accrualRate: "yearly",
        carryOverLimit: 5,
        paidLeave: true,
        requiresApproval: true
      },
      {
        name: "Sick Leave",
        category: "sick",
        description: "Medical leave for illness",
        daysPerYear: 30,
        accrualRate: "yearly",
        carryOverLimit: 0,
        paidLeave: true,
        requiresApproval: false,
        requiresDocument: true,
        documentRequiredAfterDays: 2
      },
      {
        name: "Family Responsibility Leave",
        category: "family",
        description: "Leave for family responsibilities",
        daysPerYear: 3,
        accrualRate: "yearly",
        carryOverLimit: 0,
        paidLeave: true,
        requiresApproval: true
      },
      {
        name: "Study Leave",
        category: "custom",
        description: "Leave for educational purposes",
        daysPerYear: 5,
        accrualRate: "yearly",
        carryOverLimit: 2,
        paidLeave: true,
        requiresApproval: true,
        minDaysNotice: 14
      }
    ];

    const leaveTypes = [];
    for (const leaveTypeData of leaveTypesData) {
      let leaveType = await LeaveType.findOne({ 
        company: company._id, 
        name: leaveTypeData.name 
      });
      
      if (!leaveType) {
        leaveType = new LeaveType({
          ...leaveTypeData,
          company: company._id,
          createdBy: user._id
        });
        await leaveType.save();
        console.log(`✅ Created leave type: ${leaveType.name}`);
      } else {
        console.log(`ℹ️  Leave type already exists: ${leaveType.name}`);
      }
      leaveTypes.push(leaveType);
    }

    // Create leave balances for current year
    const currentYear = new Date().getFullYear();
    console.log(`\n📊 Creating leave balances for ${currentYear}...`);

    for (const employee of employees) {
      for (const leaveType of leaveTypes) {
        const existingBalance = await LeaveBalance.findOne({
          company: company._id,
          employee: employee._id,
          leaveType: leaveType._id,
          year: currentYear
        });

        if (!existingBalance) {
          // Generate realistic balance data
          const totalAllocation = leaveType.daysPerYear;
          const used = Math.floor(Math.random() * (totalAllocation * 0.6)); // Used up to 60%
          const pending = Math.floor(Math.random() * 3); // 0-2 pending days
          const carryOver = Math.floor(Math.random() * (leaveType.carryOverLimit + 1));
          const remaining = totalAllocation + carryOver - used - pending;

          const balance = new LeaveBalance({
            company: company._id,
            employee: employee._id,
            leaveType: leaveType._id,
            year: currentYear,
            totalAllocation,
            used,
            pending,
            remaining: Math.max(0, remaining),
            carryOver,
            createdBy: user._id
          });

          await balance.save();
          console.log(`✅ Created balance for ${employee.firstName} ${employee.lastName} - ${leaveType.name}: ${remaining} remaining`);
        }
      }
    }

    // Create some leave requests
    console.log(`\n📝 Creating leave requests...`);
    
    const statuses = ["approved", "pending", "rejected"];
    const reasons = [
      "Family vacation",
      "Medical appointment",
      "Personal matters",
      "Wedding attendance",
      "Study purposes",
      "Emergency leave",
      "Rest and recuperation"
    ];

    for (let i = 0; i < 15; i++) {
      const employee = employees[Math.floor(Math.random() * employees.length)];
      const leaveType = leaveTypes[Math.floor(Math.random() * leaveTypes.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      // Generate random dates within the last 6 months
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - Math.floor(Math.random() * 180));
      
      const numberOfDays = Math.floor(Math.random() * 5) + 1; // 1-5 days
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + numberOfDays - 1);

      const leaveRequest = new LeaveRequest({
        company: company._id,
        employee: employee._id,
        leaveType: leaveType._id,
        startDate,
        endDate,
        numberOfDays,
        reason: reasons[Math.floor(Math.random() * reasons.length)],
        status,
        createdBy: user._id,
        ...(status === "approved" && {
          approvedBy: user._id,
          approvalDate: new Date(startDate.getTime() - 24 * 60 * 60 * 1000) // Approved day before
        })
      });

      await leaveRequest.save();
      console.log(`✅ Created ${status} leave request for ${employee.firstName} ${employee.lastName} - ${leaveType.name} (${numberOfDays} days)`);
    }

    console.log("\n🎉 Test leave data created successfully!");
    console.log("\nYou can now test the leave reports:");
    console.log("- Leave Days Report: Shows current leave balances");
    console.log("- Leave Expiry Report: Shows leave that will expire");
    console.log("- Leave Report: Shows leave request history");

  } catch (error) {
    console.error("❌ Error creating test leave data:", error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the script
createTestLeaveData();
