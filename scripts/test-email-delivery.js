#!/usr/bin/env node

/**
 * Email Delivery Testing Script for PandaPayroll
 * 
 * This script tests the SMTP configuration and email delivery system
 * to identify why emails show as "sent" but are not being delivered.
 */

const nodemailer = require('nodemailer');
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
const User = require('../models/user');
const Company = require('../models/Company');

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function testSMTPConfiguration() {
  console.log('\n🔧 SMTP CONFIGURATION TEST');
  console.log('='.repeat(40));

  // Display current configuration
  console.log('📧 Email Configuration:');
  console.log(`   Host: ${process.env.EMAIL_HOST}`);
  console.log(`   Port: ${process.env.EMAIL_PORT}`);
  console.log(`   User: ${process.env.EMAIL_USER}`);
  console.log(`   From Name: ${process.env.EMAIL_FROM_NAME}`);
  console.log(`   Secure: ${process.env.EMAIL_SECURE}`);
  console.log(`   Password: ${process.env.EMAIL_PASS ? '***SET***' : '❌ NOT SET'}`);

  // Create transporter with detailed logging
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    debug: true, // Enable debug logging
    logger: true // Enable logger
  });

  console.log('\n🔍 Testing SMTP Connection...');
  
  try {
    await transporter.verify();
    console.log('✅ SMTP Connection: SUCCESS');
    return transporter;
  } catch (error) {
    console.error('❌ SMTP Connection: FAILED');
    console.error('Error Details:', error.message);
    
    if (error.code === 'EAUTH') {
      console.error('🔐 Authentication failed - check email credentials');
    } else if (error.code === 'ECONNECTION') {
      console.error('🌐 Connection failed - check host and port');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('⏰ Connection timeout - check network/firewall');
    }
    
    return null;
  }
}

async function sendTestEmail(transporter, recipient) {
  console.log(`\n📧 Sending test email to: ${recipient}`);
  
  const mailOptions = {
    from: {
      name: process.env.EMAIL_FROM_NAME || "PandaPayroll Test",
      address: process.env.EMAIL_USER,
    },
    to: recipient,
    subject: '🧪 PandaPayroll Email Delivery Test',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">📧 Email Delivery Test</h1>
        </div>
        <div style="padding: 20px; background: #f8fafc;">
          <h2 style="color: #1e293b;">Test Results</h2>
          <p>If you receive this email, the SMTP configuration is working correctly.</p>
          
          <div style="background: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3 style="color: #6366f1; margin-top: 0;">Configuration Details:</h3>
            <ul>
              <li><strong>SMTP Host:</strong> ${process.env.EMAIL_HOST}</li>
              <li><strong>Port:</strong> ${process.env.EMAIL_PORT}</li>
              <li><strong>From:</strong> ${process.env.EMAIL_USER}</li>
              <li><strong>Test Time:</strong> ${new Date().toLocaleString()}</li>
            </ul>
          </div>
          
          <p style="color: #64748b; font-size: 14px;">
            This is an automated test email from PandaPayroll Email Reminder System.
          </p>
        </div>
      </div>
    `,
    headers: {
      "X-Priority": "3",
      "X-MSMail-Priority": "Normal",
      "Importance": "normal",
      "Message-ID": `<test-${Date.now()}.${Math.random().toString(36).substring(2)}@pss-group.co.za>`,
      "X-Mailer": "PandaPayroll Test System/1.0",
    },
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Test Email Sent Successfully!');
    console.log(`   Message ID: ${info.messageId}`);
    console.log(`   Response: ${info.response}`);
    
    if (info.accepted && info.accepted.length > 0) {
      console.log(`   ✅ Accepted: ${info.accepted.join(', ')}`);
    }
    
    if (info.rejected && info.rejected.length > 0) {
      console.log(`   ❌ Rejected: ${info.rejected.join(', ')}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Test Email Failed!');
    console.error(`   Error: ${error.message}`);
    
    if (error.code === 'EMESSAGE') {
      console.error('   📝 Message format error - check email content');
    } else if (error.code === 'EENVELOPE') {
      console.error('   📮 Envelope error - check sender/recipient addresses');
    }
    
    return false;
  }
}

async function checkEmailRemindersInDatabase() {
  console.log('\n📊 DATABASE EMAIL REMINDER ANALYSIS');
  console.log('='.repeat(45));

  // Check events that should have sent reminders
  const eventsWithReminders = await PayrollCalendarEvent.find({
    'emailReminders.0': { $exists: true }
  }).populate('company', 'name');

  console.log(`\n📧 Events with Email Reminders Sent: ${eventsWithReminders.length}`);

  for (const event of eventsWithReminders) {
    console.log(`\n📌 Event: ${event.title}`);
    console.log(`   Company: ${event.company?.name || 'Unknown'}`);
    console.log(`   Reminders Sent: ${event.emailReminders.length}`);
    
    event.emailReminders.forEach((reminder, index) => {
      console.log(`   ${index + 1}. ${reminder.daysBeforeEvent} days before - ${reminder.sentAt.toISOString()}`);
      console.log(`      Status: ${reminder.emailStatus}`);
      console.log(`      Recipients: ${reminder.sentTo.length}`);
      reminder.sentTo.forEach(recipient => {
        console.log(`        - ${recipient.email} (${recipient.name || 'Unknown'})`);
      });
      if (reminder.messageId) {
        console.log(`      Message ID: ${reminder.messageId}`);
      }
      if (reminder.errorMessage) {
        console.log(`      ❌ Error: ${reminder.errorMessage}`);
      }
    });
  }

  return eventsWithReminders;
}

async function testPayrollReminderService() {
  console.log('\n🔄 TESTING PAYROLL REMINDER SERVICE');
  console.log('='.repeat(40));

  try {
    const PayrollReminderService = require('../services/payrollReminderService');
    
    console.log('📧 Running email reminder processing...');
    const result = await PayrollReminderService.processEmailReminders();
    
    console.log('✅ Reminder Service Results:');
    console.log(`   Sent: ${result.successCount}`);
    console.log(`   Failed: ${result.failureCount}`);
    console.log(`   Total Processed: ${result.totalProcessed}`);
    
    return result;
  } catch (error) {
    console.error('❌ Reminder Service Failed:', error.message);
    return null;
  }
}

async function checkZohoSpecificIssues() {
  console.log('\n🔍 ZOHO-SPECIFIC TROUBLESHOOTING');
  console.log('='.repeat(35));

  console.log('📋 Common Zoho Mail Issues:');
  console.log('   1. App-specific passwords required for SMTP');
  console.log('   2. Two-factor authentication blocking');
  console.log('   3. Rate limiting for bulk emails');
  console.log('   4. Domain verification requirements');
  console.log('   5. SPF/DKIM configuration needed');

  console.log('\n🔧 Recommended Checks:');
  console.log('   1. Verify <EMAIL> can send emails manually');
  console.log('   2. Check if app-specific password is being used');
  console.log('   3. Verify domain SPF record includes Zoho');
  console.log('   4. Check Zoho admin panel for blocked/failed emails');
  console.log('   5. Test with a different "from" address');

  // Test with different configurations
  console.log('\n🧪 Testing Alternative Configurations...');
  
  // Test 1: Different port
  console.log('\n   Test 1: Port 465 with SSL');
  const transporter465 = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: 465,
    secure: true,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });

  try {
    await transporter465.verify();
    console.log('   ✅ Port 465 SSL: Connection successful');
  } catch (error) {
    console.log('   ❌ Port 465 SSL: Failed -', error.message);
  }

  // Test 2: Port 25
  console.log('\n   Test 2: Port 25');
  const transporter25 = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: 25,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });

  try {
    await transporter25.verify();
    console.log('   ✅ Port 25: Connection successful');
  } catch (error) {
    console.log('   ❌ Port 25: Failed -', error.message);
  }
}

async function main() {
  console.log('🧪 PandaPayroll Email Delivery Troubleshooting');
  console.log('='.repeat(50));
  console.log(`⏰ Test Time: ${new Date().toLocaleString()}`);

  try {
    // Connect to database
    await connectToDatabase();

    // Test SMTP configuration
    const transporter = await testSMTPConfiguration();
    
    if (transporter) {
      // Send test email to the user
      const testRecipient = '<EMAIL>';
      await sendTestEmail(transporter, testRecipient);
    }

    // Check database for email reminders
    await checkEmailRemindersInDatabase();

    // Test the actual reminder service
    await testPayrollReminderService();

    // Zoho-specific troubleshooting
    await checkZohoSpecificIssues();

    console.log('\n✅ Email Delivery Test Complete');
    console.log('\n📋 Next Steps:');
    console.log('1. Check your email inbox for the test email');
    console.log('2. If test email arrives, the SMTP config is working');
    console.log('3. If no test email, check Zoho admin panel for blocks');
    console.log('4. Verify app-specific password is being used');
    console.log('5. Check spam/junk folders');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

if (require.main === module) {
  main();
}

module.exports = { testSMTPConfiguration, sendTestEmail, checkEmailRemindersInDatabase };
