#!/usr/bin/env node

/**
 * Dashboard JavaScript Function Checker
 * 
 * This script checks if the required JavaScript functions exist in the dashboard
 */

const fs = require('fs');
const path = require('path');

function checkDashboardJS() {
  console.log('🔍 Checking Dashboard JavaScript Functions');
  console.log('='.repeat(50));

  try {
    // Read the dashboard.ejs file
    const dashboardPath = path.join(__dirname, '../views/dashboard.ejs');
    const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');

    // Check for required functions
    const requiredFunctions = [
      'sendEmailReminders',
      'showReminderStatus', 
      'showConfirmationToast',
      'displayReminderStatusModal',
      'closeReminderStatusModal',
      'attachEventListeners'
    ];

    console.log('📋 Function Availability Check:');
    requiredFunctions.forEach(func => {
      const functionRegex = new RegExp(`function\\s+${func}\\s*\\(|${func}\\s*=\\s*function|${func}\\s*:\\s*function`, 'i');
      const asyncFunctionRegex = new RegExp(`async\\s+function\\s+${func}\\s*\\(|${func}\\s*=\\s*async\\s+function`, 'i');
      
      const hasFunction = functionRegex.test(dashboardContent);
      const hasAsyncFunction = asyncFunctionRegex.test(dashboardContent);
      
      if (hasFunction || hasAsyncFunction) {
        console.log(`  ✅ ${func}: Found ${hasAsyncFunction ? '(async)' : ''}`);
      } else {
        console.log(`  ❌ ${func}: Missing`);
      }
    });

    // Check for event listener attachments
    console.log('\n📋 Event Listener Check:');
    const eventListenerChecks = [
      { name: 'sendRemindersBtn click', pattern: /sendRemindersBtn.*addEventListener.*click|sendRemindersBtn.*onclick/i },
      { name: 'reminderStatusBtn click', pattern: /reminderStatusBtn.*addEventListener.*click|reminderStatusBtn.*onclick/i },
      { name: 'attachEventListeners call', pattern: /attachEventListeners\s*\(\s*\)/i }
    ];

    eventListenerChecks.forEach(check => {
      const found = check.pattern.test(dashboardContent);
      console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });

    // Check for DOM element selectors
    console.log('\n📋 DOM Element Selector Check:');
    const selectorChecks = [
      { name: 'sendRemindersBtn', pattern: /getElementById\s*\(\s*['"']sendRemindersBtn['"']\s*\)/i },
      { name: 'reminderStatusBtn', pattern: /getElementById\s*\(\s*['"']reminderStatusBtn['"']\s*\)/i },
      { name: 'reminderStatusModal', pattern: /getElementById\s*\(\s*['"']reminderStatusModal['"']\s*\)/i }
    ];

    selectorChecks.forEach(check => {
      const found = check.pattern.test(dashboardContent);
      console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });

    // Check for API endpoints
    console.log('\n📋 API Endpoint Check:');
    const apiChecks = [
      { name: 'send-reminders endpoint', pattern: /\/api\/payroll-calendar\/send-reminders/i },
      { name: 'reminder-status endpoint', pattern: /\/api\/payroll-calendar\/reminder-status/i }
    ];

    apiChecks.forEach(check => {
      const found = check.pattern.test(dashboardContent);
      console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });

    // Check for Bootstrap dropdown functionality
    console.log('\n📋 Bootstrap Dropdown Check:');
    const bootstrapChecks = [
      { name: 'data-bs-toggle="dropdown"', pattern: /data-bs-toggle\s*=\s*['"']dropdown['"']/i },
      { name: 'dropdown-menu class', pattern: /class\s*=\s*['"'][^'"]*dropdown-menu[^'"]*['"']/i },
      { name: 'dropdown-item class', pattern: /class\s*=\s*['"'][^'"]*dropdown-item[^'"]*['"']/i }
    ];

    bootstrapChecks.forEach(check => {
      const found = check.pattern.test(dashboardContent);
      console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });

    // Check for Notifications class usage
    console.log('\n📋 Notifications Class Check:');
    const notificationChecks = [
      { name: 'Notifications.success', pattern: /Notifications\.success\s*\(/i },
      { name: 'Notifications.error', pattern: /Notifications\.error\s*\(/i },
      { name: 'notifications.js include', pattern: /src\s*=\s*['"'][^'"]*notifications\.js['"']/i }
    ];

    notificationChecks.forEach(check => {
      const found = check.pattern.test(dashboardContent);
      console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
    });

    // Extract and analyze the DOMContentLoaded section
    console.log('\n📋 DOMContentLoaded Analysis:');
    const domContentLoadedMatch = dashboardContent.match(/document\.addEventListener\s*\(\s*['"']DOMContentLoaded['"']\s*,\s*function\s*\(\s*\)\s*\{([\s\S]*?)\}\s*\)\s*;/i);
    
    if (domContentLoadedMatch) {
      console.log('  ✅ DOMContentLoaded event listener found');
      const domContentCode = domContentLoadedMatch[1];
      
      const domChecks = [
        { name: 'attachEventListeners call', pattern: /attachEventListeners\s*\(\s*\)/i },
        { name: 'initializePayrollCalendar call', pattern: /initializePayrollCalendar\s*\(\s*\)/i },
        { name: 'loadPayrollEvents call', pattern: /loadPayrollEvents\s*\(\s*\)/i }
      ];

      domChecks.forEach(check => {
        const found = check.pattern.test(domContentCode);
        console.log(`    ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Called' : 'Missing'}`);
      });
    } else {
      console.log('  ❌ DOMContentLoaded event listener not found');
    }

    console.log('\n✅ Dashboard JavaScript Analysis Complete');

  } catch (error) {
    console.error('❌ Error reading dashboard file:', error);
  }
}

function checkNotificationsJS() {
  console.log('\n🔍 Checking Notifications.js File');
  console.log('='.repeat(35));

  try {
    const notificationsPath = path.join(__dirname, '../public/js/notifications.js');
    
    if (fs.existsSync(notificationsPath)) {
      console.log('✅ notifications.js file exists');
      
      const notificationsContent = fs.readFileSync(notificationsPath, 'utf8');
      
      const checks = [
        { name: 'Notifications class', pattern: /class\s+Notifications/i },
        { name: 'show method', pattern: /static\s+show\s*\(/i },
        { name: 'success method', pattern: /static\s+success\s*\(/i },
        { name: 'error method', pattern: /static\s+error\s*\(/i },
        { name: 'window.Notifications export', pattern: /window\.Notifications\s*=\s*Notifications/i }
      ];

      checks.forEach(check => {
        const found = check.pattern.test(notificationsContent);
        console.log(`  ${found ? '✅' : '❌'} ${check.name}: ${found ? 'Found' : 'Missing'}`);
      });
    } else {
      console.log('❌ notifications.js file does not exist');
    }
  } catch (error) {
    console.error('❌ Error checking notifications.js:', error);
  }
}

function main() {
  checkDashboardJS();
  checkNotificationsJS();
  
  console.log('\n💡 Next Steps:');
  console.log('1. Check browser console for JavaScript errors');
  console.log('2. Verify Bootstrap 5 is loaded correctly');
  console.log('3. Test dropdown functionality manually');
  console.log('4. Check network tab for failed API requests');
}

if (require.main === module) {
  main();
}

module.exports = { checkDashboardJS, checkNotificationsJS };
