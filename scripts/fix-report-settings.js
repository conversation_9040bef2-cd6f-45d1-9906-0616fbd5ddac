#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix existing ReportSettings documents that have invalid enum values
 * This script will find and update any ReportSettings documents where
 * employeeReports.default is set to "currentMonth" (invalid) and change it to "allTime" (valid)
 */

const mongoose = require('mongoose');
const ReportSettings = require('../models/ReportSettings');

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/pandapayroll', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

const fixReportSettings = async () => {
  try {
    console.log('🔍 Searching for ReportSettings with invalid employeeReports.default values...');
    
    // Find all ReportSettings documents where employeeReports.default is "currentMonth"
    const invalidSettings = await ReportSettings.find({
      'dateRangePresets.employeeReports.default': 'currentMonth'
    });

    console.log(`📊 Found ${invalidSettings.length} ReportSettings documents with invalid values`);

    if (invalidSettings.length === 0) {
      console.log('✅ No invalid ReportSettings found. All documents are valid.');
      return;
    }

    // Update each invalid document
    let updatedCount = 0;
    for (const setting of invalidSettings) {
      try {
        console.log(`🔧 Fixing ReportSettings for company: ${setting.company}`);
        
        // Update the invalid value
        setting.dateRangePresets.employeeReports.default = 'allTime';
        setting.updatedAt = new Date();
        
        await setting.save();
        updatedCount++;
        
        console.log(`✅ Updated ReportSettings for company: ${setting.company}`);
      } catch (error) {
        console.error(`❌ Failed to update ReportSettings for company: ${setting.company}`, error.message);
      }
    }

    console.log(`\n🎉 Successfully updated ${updatedCount} out of ${invalidSettings.length} ReportSettings documents`);
    
    // Verify the fix
    const remainingInvalid = await ReportSettings.find({
      'dateRangePresets.employeeReports.default': 'currentMonth'
    });
    
    if (remainingInvalid.length === 0) {
      console.log('✅ Verification passed: No invalid ReportSettings remain');
    } else {
      console.log(`⚠️  Warning: ${remainingInvalid.length} invalid ReportSettings still exist`);
    }

  } catch (error) {
    console.error('❌ Error during ReportSettings fix:', error);
  }
};

const main = async () => {
  console.log('🚀 Starting ReportSettings validation fix script...\n');
  
  await connectDB();
  await fixReportSettings();
  
  console.log('\n🏁 Script completed. Closing database connection...');
  await mongoose.connection.close();
  process.exit(0);
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { fixReportSettings };
