#!/usr/bin/env node

/**
 * Fix Test Event Recipients Script
 * 
 * This script fixes the recipient issue by either:
 * 1. Moving test events to a company with admin users
 * 2. Adding admin users to the test company
 * 3. Setting up proper company ownership
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
const User = require('../models/user');
const Company = require('../models/Company');

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function analyzeCurrentSituation() {
  console.log('\n🔍 ANALYZING CURRENT SITUATION');
  console.log('='.repeat(40));

  // Find test events
  const testEvents = await PayrollCalendarEvent.find({
    'metadata.tags': 'test'
  }).populate('company', 'name owner');

  console.log(`📧 Found ${testEvents.length} test events`);

  // Find companies with admin users
  const companies = await Company.find().populate('owner', 'email firstName lastName');
  
  console.log('\n🏢 Company Analysis:');
  for (const company of companies) {
    const adminUsers = await User.find({
      companies: company._id,
      isVerified: true,
      email: { $exists: true, $ne: null, $ne: '' },
      $or: [
        { roleName: 'owner' },
        { roleName: 'admin' },
        { roleName: 'manager' },
        { roleName: 'hr' },
        { roleName: 'payroll' }
      ]
    }).select('email firstName lastName roleName');

    console.log(`   ${company.name} (${company._id})`);
    console.log(`     Owner: ${company.owner ? company.owner.email : 'None'}`);
    console.log(`     Admin Users: ${adminUsers.length}`);
    
    if (adminUsers.length > 0) {
      adminUsers.forEach(user => {
        console.log(`       - ${user.email} (${user.roleName})`);
      });
    }

    const companyEvents = testEvents.filter(event => 
      event.company._id.toString() === company._id.toString()
    );
    console.log(`     Test Events: ${companyEvents.length}`);
  }

  return { testEvents, companies };
}

async function fixOption1_MoveEventsToWorkingCompany() {
  console.log('\n🔧 OPTION 1: Move test events to company with admin users');
  console.log('='.repeat(60));

  // Find a company with admin users (preferably Panda Solutions)
  const targetCompany = await Company.findOne({
    name: { $regex: /panda/i }
  });

  if (!targetCompany) {
    console.log('❌ Could not find Panda Solutions company');
    return false;
  }

  // Verify it has admin users
  const adminUsers = await User.find({
    companies: targetCompany._id,
    isVerified: true,
    email: { $exists: true, $ne: null, $ne: '' },
    $or: [
      { roleName: 'owner' },
      { roleName: 'admin' },
      { roleName: 'manager' },
      { roleName: 'hr' },
      { roleName: 'payroll' }
    ]
  });

  if (adminUsers.length === 0) {
    console.log('❌ Target company has no admin users');
    return false;
  }

  console.log(`✅ Target Company: ${targetCompany.name}`);
  console.log(`✅ Admin Users: ${adminUsers.length}`);
  adminUsers.forEach(user => {
    console.log(`   - ${user.email} (${user.roleName})`);
  });

  // Update test events
  const result = await PayrollCalendarEvent.updateMany(
    { 'metadata.tags': 'test' },
    { 
      company: targetCompany._id,
      assignedTo: [adminUsers[0]._id] // Assign to first admin user
    }
  );

  console.log(`✅ Updated ${result.modifiedCount} test events`);
  console.log(`📧 Events now assigned to: ${adminUsers[0].email}`);

  return true;
}

async function fixOption2_AddAdminToTestCompany() {
  console.log('\n🔧 OPTION 2: Add admin user to test company');
  console.log('='.repeat(45));

  // Find the test company (Surevest)
  const testCompany = await Company.findOne({
    name: { $regex: /surevest/i }
  });

  if (!testCompany) {
    console.log('❌ Could not find Surevest company');
    return false;
  }

  // Find your user account
  const adminUser = await User.findOne({
    email: '<EMAIL>'
  });

  if (!adminUser) {
    console.log('❌ Could not find admin user');
    return false;
  }

  // Add user to company if not already added
  if (!adminUser.companies.includes(testCompany._id)) {
    adminUser.companies.push(testCompany._id);
    await adminUser.save();
    console.log(`✅ Added ${adminUser.email} to ${testCompany.name}`);
  } else {
    console.log(`✅ ${adminUser.email} already in ${testCompany.name}`);
  }

  // Set as company owner if not set
  if (!testCompany.owner) {
    testCompany.owner = adminUser._id;
    await testCompany.save();
    console.log(`✅ Set ${adminUser.email} as owner of ${testCompany.name}`);
  }

  // Assign test events to this user
  const result = await PayrollCalendarEvent.updateMany(
    { 'metadata.tags': 'test' },
    { assignedTo: [adminUser._id] }
  );

  console.log(`✅ Assigned ${result.modifiedCount} test events to ${adminUser.email}`);

  return true;
}

async function testEmailRemindersAfterFix() {
  console.log('\n🧪 TESTING EMAIL REMINDERS AFTER FIX');
  console.log('='.repeat(40));

  try {
    const PayrollReminderService = require('../services/payrollReminderService');
    
    console.log('📧 Running email reminder processing...');
    const result = await PayrollReminderService.processEmailReminders();
    
    console.log('✅ Reminder Service Results:');
    console.log(`   Sent: ${result.successCount}`);
    console.log(`   Failed: ${result.failureCount}`);
    console.log(`   Total Processed: ${result.totalProcessed}`);
    
    if (result.successCount > 0) {
      console.log('🎉 SUCCESS! Emails should now be sent and delivered!');
    } else {
      console.log('⚠️  Still no emails sent - may need further investigation');
    }
    
    return result;
  } catch (error) {
    console.error('❌ Reminder Service Failed:', error.message);
    return null;
  }
}

async function main() {
  console.log('🔧 PandaPayroll Email Recipient Fix Tool');
  console.log('='.repeat(45));

  try {
    await connectToDatabase();

    // Analyze current situation
    const { testEvents, companies } = await analyzeCurrentSituation();

    if (testEvents.length === 0) {
      console.log('❌ No test events found');
      return;
    }

    // Try Option 1 first (move to working company)
    console.log('\n🎯 Attempting to fix recipient issue...');
    
    let fixed = await fixOption1_MoveEventsToWorkingCompany();
    
    if (!fixed) {
      console.log('\n🔄 Trying alternative approach...');
      fixed = await fixOption2_AddAdminToTestCompany();
    }

    if (fixed) {
      console.log('\n✅ Recipients fixed! Testing email reminders...');
      await testEmailRemindersAfterFix();
    } else {
      console.log('\n❌ Could not fix recipient issue automatically');
      console.log('💡 Manual steps required:');
      console.log('1. Add admin users to Surevest company');
      console.log('2. Or move test events to Panda Solutions company');
      console.log('3. Or assign test events to specific users');
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixOption1_MoveEventsToWorkingCompany, fixOption2_AddAdminToTestCompany };
