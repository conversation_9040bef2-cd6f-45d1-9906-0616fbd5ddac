#!/usr/bin/env node

/**
 * Email Reminder Diagnostic Tool for PandaPayroll
 * 
 * This script helps diagnose issues with the email reminder system by:
 * 1. Checking for existing compliance events
 * 2. Analyzing reminder settings and configurations
 * 3. Simulating the reminder logic
 * 4. Providing detailed troubleshooting information
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const PayrollCalendarEvent = require('../models/PayrollCalendarEvent');
const User = require('../models/user');
const Company = require('../models/Company');

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function diagnoseEmailReminders() {
  console.log('\n🔍 PandaPayroll Email Reminder Diagnostic Tool');
  console.log('='.repeat(60));

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  console.log(`📅 Current Date: ${today.toISOString().split('T')[0]}`);
  console.log(`⏰ Analysis Time: ${new Date().toLocaleString()}`);

  // 1. Check total events in database
  console.log('\n📊 DATABASE ANALYSIS');
  console.log('-'.repeat(30));
  
  const totalEvents = await PayrollCalendarEvent.countDocuments();
  console.log(`Total Events in Database: ${totalEvents}`);

  if (totalEvents === 0) {
    console.log('❌ NO EVENTS FOUND - This is likely the issue!');
    console.log('💡 Solution: Create compliance events using "Setup Compliance" button');
    return;
  }

  // 2. Analyze events by status
  const eventsByStatus = await PayrollCalendarEvent.aggregate([
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);
  
  console.log('\nEvents by Status:');
  eventsByStatus.forEach(status => {
    console.log(`  ${status._id}: ${status.count}`);
  });

  // 3. Check future events (not completed)
  const futureEvents = await PayrollCalendarEvent.find({
    status: { $ne: 'completed' },
    date: { $gte: today }
  });
  
  console.log(`\nFuture Events (not completed): ${futureEvents.length}`);

  // 4. Check events with email reminders enabled
  const eventsWithEmailReminders = await PayrollCalendarEvent.find({
    'reminderSettings.enabled': true,
    'reminderSettings.emailReminder': true,
    status: { $ne: 'completed' },
    date: { $gte: today }
  });
  
  console.log(`Events with Email Reminders Enabled: ${eventsWithEmailReminders.length}`);

  // 5. Detailed analysis of each event
  console.log('\n📋 DETAILED EVENT ANALYSIS');
  console.log('-'.repeat(40));

  for (const event of eventsWithEmailReminders) {
    const daysUntilEvent = Math.ceil((event.date - today) / (1000 * 60 * 60 * 24));
    
    console.log(`\n📌 Event: ${event.title}`);
    console.log(`   Type: ${event.type}`);
    console.log(`   Date: ${event.date.toISOString().split('T')[0]}`);
    console.log(`   Days Until: ${daysUntilEvent}`);
    console.log(`   Status: ${event.status}`);
    console.log(`   Company: ${event.company}`);
    console.log(`   Reminder Days: [${event.reminderSettings.daysBeforeEvent.join(', ')}]`);
    console.log(`   Email Reminders: ${event.reminderSettings.emailReminder}`);
    console.log(`   Reminders Enabled: ${event.reminderSettings.enabled}`);
    
    // Check if reminder should be sent today
    const shouldSendToday = event.reminderSettings.daysBeforeEvent.includes(daysUntilEvent);
    console.log(`   Should Send Today: ${shouldSendToday ? '✅ YES' : '❌ NO'}`);
    
    // Check if already sent
    const alreadySent = event.emailReminders.some(
      reminder => reminder.daysBeforeEvent === daysUntilEvent
    );
    console.log(`   Already Sent: ${alreadySent ? '✅ YES' : '❌ NO'}`);
    
    if (event.emailReminders.length > 0) {
      console.log(`   Previous Reminders:`);
      event.emailReminders.forEach(reminder => {
        console.log(`     - ${reminder.daysBeforeEvent} days before (${reminder.sentAt.toISOString().split('T')[0]})`);
      });
    }
  }

  // 6. Check companies and users
  console.log('\n👥 USER & COMPANY ANALYSIS');
  console.log('-'.repeat(35));
  
  const companies = await Company.find();
  console.log(`Total Companies: ${companies.length}`);
  
  for (const company of companies) {
    console.log(`\n🏢 Company: ${company.name} (${company._id})`);
    
    const companyUsers = await User.find({
      companies: company._id,
      isVerified: true,
      email: { $exists: true, $ne: null, $ne: '' }
    }).select('email firstName lastName roleName');
    
    console.log(`   Total Users: ${companyUsers.length}`);
    
    const adminUsers = companyUsers.filter(user => 
      ['owner', 'admin', 'manager', 'hr', 'payroll'].includes(user.roleName)
    );
    
    console.log(`   Admin Users: ${adminUsers.length}`);
    adminUsers.forEach(user => {
      console.log(`     - ${user.email} (${user.roleName})`);
    });
    
    const companyEvents = eventsWithEmailReminders.filter(event => 
      event.company.toString() === company._id.toString()
    );
    console.log(`   Company Events: ${companyEvents.length}`);
  }

  // 7. Simulate reminder logic
  console.log('\n🔄 REMINDER SIMULATION');
  console.log('-'.repeat(25));
  
  let simulatedReminders = 0;
  
  for (const event of eventsWithEmailReminders) {
    const daysUntilEvent = Math.ceil((event.date - today) / (1000 * 60 * 60 * 24));
    
    for (const reminderDay of event.reminderSettings.daysBeforeEvent) {
      if (daysUntilEvent === reminderDay) {
        const alreadySent = event.emailReminders.some(
          reminder => reminder.daysBeforeEvent === reminderDay
        );
        
        if (!alreadySent) {
          console.log(`📧 Would send reminder for: ${event.title} (${reminderDay} days before)`);
          simulatedReminders++;
        }
      }
    }
  }
  
  console.log(`\nTotal Reminders That Would Be Sent: ${simulatedReminders}`);

  // 8. Recommendations
  console.log('\n💡 RECOMMENDATIONS');
  console.log('-'.repeat(20));
  
  if (totalEvents === 0) {
    console.log('1. ❗ Create compliance events using the "Setup Compliance" button');
    console.log('2. 📅 Ensure events have future dates');
    console.log('3. ⚙️ Verify reminder settings are enabled');
  } else if (eventsWithEmailReminders.length === 0) {
    console.log('1. ✅ Check that events have reminderSettings.enabled = true');
    console.log('2. ✅ Check that events have reminderSettings.emailReminder = true');
    console.log('3. ✅ Verify events are not marked as completed');
    console.log('4. ✅ Ensure event dates are in the future');
  } else if (simulatedReminders === 0) {
    console.log('1. 📅 No events need reminders today based on their reminder schedules');
    console.log('2. 🔄 Try creating a test event with a date that matches reminder criteria');
    console.log('3. ⏰ Check if reminders have already been sent for today\'s criteria');
  } else {
    console.log('1. ✅ System should be working - check email configuration');
    console.log('2. 📧 Verify SMTP settings and email credentials');
    console.log('3. 🔍 Check server logs for email sending errors');
  }

  console.log('\n✅ Diagnostic Complete');
}

async function main() {
  try {
    await connectToDatabase();
    await diagnoseEmailReminders();
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

if (require.main === module) {
  main();
}

module.exports = { diagnoseEmailReminders };
