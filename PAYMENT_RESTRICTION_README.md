# Payment Restriction Feature - Read-Only Mode for Overdue Payments

## Overview

The Payment Restriction feature automatically places PandaPayroll accounts into read-only mode when payments are overdue by more than 60 days. This ensures service sustainability while maintaining compliance access for users.

## Key Features

### 🔒 **Automatic Restriction**
- Triggers when any invoice is 60+ days overdue
- Seamless transition to read-only mode
- Preserves all existing data and view access

### 📊 **Compliance Maintained**
- View-only access to all company and employee data
- Export capabilities for compliance purposes
- Access to reports and existing documents
- Payslip viewing remains available

### 🚫 **Restricted Operations**
- No new leave requests
- No payroll processing or calculations
- No employee data modifications
- No new document creation
- No administrative changes

### 💳 **Easy Recovery**
- Immediate restoration upon payment
- Clear payment instructions and links
- Automatic detection of payment status

## Technical Implementation

### Database Models

#### Invoice Model Enhancements
```javascript
// Check if account should be restricted (60+ days overdue)
invoiceSchema.methods.shouldRestrictAccount = function() {
  if (this.status === 'paid') return false;
  const daysOverdue = Math.floor((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
  return daysOverdue >= 60;
};

// Get days overdue for an invoice
invoiceSchema.methods.getDaysOverdue = function() {
  if (this.status === 'paid') return 0;
  const daysOverdue = Math.floor((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
  return Math.max(0, daysOverdue);
};
```

### Middleware Implementation

#### Billing Restriction Middleware (`middleware/billingRestriction.js`)
- Checks for overdue invoices on each request
- Automatically detects 60+ day overdue payments
- Blocks non-GET requests for restricted accounts
- Redirects to payment restriction page
- Allows billing-related operations

#### Key Routes Protected
- Leave request creation (`/leave/*`)
- Payroll processing (`/payroll/*`)
- Employee management (`/employees/*`)
- Administrative functions (`/settings/*`)
- Company modifications

#### Unrestricted Routes
- Billing and payment pages (`/billing/*`)
- Authentication (`/auth/*`, `/login`)
- Dashboard viewing (`/dashboard`)
- API endpoints for viewing data

### Client-Side Implementation

#### Payment Restriction Script (`public/js/payment-restriction.js`)
- Disables form elements and buttons
- Shows contextual warning banners
- Provides toast notifications
- Maintains payment-related functionality
- Visual indicators for restricted state

#### Features:
- **Warning Banner**: Persistent top banner with payment details
- **Form Disabling**: All non-payment forms become read-only
- **Button Restrictions**: Action buttons disabled with tooltips
- **Toast Notifications**: Context-aware messages for restricted actions
- **Visual Styling**: Clear indication of restricted state

### User Interface

#### Payment Restricted Page (`views/billing/payment-restricted.ejs`)
- Dedicated page explaining the restriction
- Clear payment instructions
- Invoice details display
- Direct payment links
- Information about available functionality

#### Template Integration
- Payment restriction partial included in key templates
- Automatic detection and application of restrictions
- Consistent styling with PandaPayroll design system

## Configuration

### Environment Variables
No additional environment variables required. Uses existing billing and database configuration.

### Dependencies
- Existing PandaPayroll infrastructure
- MongoDB for invoice tracking
- Express.js middleware system

## Usage

### For Administrators

#### Monitoring Payment Status
1. Check `/billing/preferences` for payment status
2. Review pending invoices section
3. Monitor days overdue indicators

#### Resolving Restrictions
1. Process overdue payments through Paystack
2. System automatically detects payment
3. Restrictions lift within minutes of payment

### For Users

#### When Restricted
1. System displays clear warning banners
2. Read-only access to all data maintained
3. Export and compliance functions available
4. Direct payment links provided

#### Available Actions in Read-Only Mode
- ✅ View all employee and company data
- ✅ Access reports and analytics
- ✅ Download existing documents
- ✅ Export data for compliance
- ✅ View payslips and historical data
- ❌ Create new leave requests
- ❌ Modify employee information
- ❌ Process payroll
- ❌ Create new documents

## Testing

### Manual Testing
1. Create test invoice 60+ days overdue
2. Access restricted functionality
3. Verify read-only mode activation
4. Test payment restoration

### Automated Testing
Run the test script:
```bash
node test-payment-restriction.js
```

### Test Scenarios
- Invoice 60+ days overdue → Restriction active
- Invoice < 60 days overdue → No restriction
- Paid invoice → No restriction regardless of due date
- Multiple overdue invoices → Uses oldest for calculation

## Integration Points

### Existing Systems
- **Billing System**: Leverages existing invoice tracking
- **Authentication**: Works with current auth middleware
- **UI Framework**: Integrates with PandaPayroll design system
- **Notification System**: Uses established toast notifications

### API Compatibility
- All existing API endpoints remain functional for viewing
- POST/PUT/DELETE operations blocked for restricted accounts
- Billing API endpoints remain fully functional

## Security Considerations

- Restriction bypass prevention through middleware
- Session-based restriction tracking
- Secure payment processing integration
- Data access logging maintained

## Maintenance

### Regular Monitoring
- Invoice status checking automated
- Payment detection real-time
- Restriction status in user sessions

### Updates and Modifications
- Restriction logic in `middleware/billingRestriction.js`
- UI components in `public/js/payment-restriction.js`
- Styling in component CSS files

## Support

For technical issues or questions about the payment restriction feature:
1. Check application logs for restriction triggers
2. Verify invoice status in database
3. Test payment processing functionality
4. Review middleware execution order

## Future Enhancements

### Potential Improvements
- Graduated restrictions (warning → limited → full restriction)
- Email notifications before restriction
- Grace period configuration
- Custom restriction messages
- Detailed restriction analytics
