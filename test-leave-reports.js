const mongoose = require("mongoose");
const ReportController = require("./controllers/reportController");
const Company = require("./models/company");
const Employee = require("./models/Employee");
const LeaveType = require("./models/LeaveType");
const LeaveBalance = require("./models/LeaveBalance");
const LeaveRequest = require("./models/LeaveRequest");

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testLeaveReports() {
  try {
    console.log("🧪 Testing Leave Reports...");

    // Find the test company
    const company = await Company.findOne({ companyCode: "CEA08B3B" });
    if (!company) {
      console.error("❌ Test company not found");
      return;
    }
    console.log(`✅ Using company: ${company.name} (${company._id})`);

    // Test 1: Leave Days Report
    console.log("\n📊 Testing Leave Days Report...");
    try {
      const leaveDaysReport = await ReportController.generateLeaveDaysReport(
        company._id,
        "excel",
        {},
        null, // selectedEmployees
        null  // selectedLeaveTypes
      );
      
      if (leaveDaysReport && leaveDaysReport.length > 0) {
        console.log(`✅ Leave Days Report generated successfully (${leaveDaysReport.length} bytes)`);
      } else {
        console.log("⚠️  Leave Days Report generated but appears empty");
      }
    } catch (error) {
      console.error("❌ Leave Days Report failed:", error.message);
    }

    // Test 2: Leave Expiry Report
    console.log("\n⏰ Testing Leave Expiry Report...");
    try {
      const leaveExpiryReport = await ReportController.generateLeaveExpiryReport(
        company._id,
        "excel",
        {},
        null, // selectedEmployees
        null  // dateRange
      );
      
      if (leaveExpiryReport && leaveExpiryReport.length > 0) {
        console.log(`✅ Leave Expiry Report generated successfully (${leaveExpiryReport.length} bytes)`);
      } else {
        console.log("⚠️  Leave Expiry Report generated but appears empty");
      }
    } catch (error) {
      console.error("❌ Leave Expiry Report failed:", error.message);
    }

    // Test 3: Leave Report (Leave Request History)
    console.log("\n📝 Testing Leave Report (Request History)...");
    try {
      const leaveReport = await ReportController.generateLeaveReport(
        company._id,
        "excel",
        {},
        null, // selectedEmployees
        null  // dateRange
      );
      
      if (leaveReport && leaveReport.length > 0) {
        console.log(`✅ Leave Report generated successfully (${leaveReport.length} bytes)`);
      } else {
        console.log("⚠️  Leave Report generated but appears empty");
      }
    } catch (error) {
      console.error("❌ Leave Report failed:", error.message);
    }

    // Check data availability
    console.log("\n📋 Checking data availability...");
    
    const employees = await Employee.find({ company: company._id, status: "Active" });
    console.log(`👥 Active employees: ${employees.length}`);
    
    const leaveTypes = await LeaveType.find({ company: company._id, active: true });
    console.log(`📋 Active leave types: ${leaveTypes.length}`);
    
    const currentYear = new Date().getFullYear();
    const leaveBalances = await LeaveBalance.find({ 
      company: company._id, 
      year: currentYear 
    });
    console.log(`💰 Leave balances for ${currentYear}: ${leaveBalances.length}`);
    
    const leaveRequests = await LeaveRequest.find({ company: company._id });
    console.log(`📝 Leave requests: ${leaveRequests.length}`);

    if (leaveTypes.length > 0) {
      console.log("\n📋 Leave Types:");
      leaveTypes.forEach(lt => {
        console.log(`  - ${lt.name} (${lt.category}): ${lt.daysPerYear} days/year`);
      });
    }

    if (leaveBalances.length > 0) {
      console.log("\n💰 Sample Leave Balances:");
      const sampleBalances = leaveBalances.slice(0, 3);
      for (const balance of sampleBalances) {
        await balance.populate("employee", "firstName lastName");
        await balance.populate("leaveType", "name");
        console.log(`  - ${balance.employee.firstName} ${balance.employee.lastName}: ${balance.leaveType.name} - ${balance.remaining} remaining`);
      }
    }

    console.log("\n🎉 Leave Reports testing completed!");

  } catch (error) {
    console.error("❌ Error testing leave reports:", error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
testLeaveReports();
