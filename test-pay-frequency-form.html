<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pay Frequency Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select { padding: 8px; width: 200px; }
        .frequency-options { margin-top: 10px; padding: 10px; border: 1px solid #ccc; }
        .hidden { display: none; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>Pay Frequency Form Validation Test</h1>
    
    <form id="payFrequencyForm">
        <div class="form-group">
            <label for="frequency">Frequency *</label>
            <select id="frequency" name="frequency" required>
                <option value="monthly">Monthly</option>
                <option value="weekly">Weekly</option>
                <option value="bi-weekly">Bi-weekly</option>
            </select>
        </div>

        <div id="monthlyOptions" class="frequency-options">
            <div class="form-group">
                <label for="lastDayOfMonth">Last day of month *</label>
                <select id="lastDayOfMonth" name="lastDayOfMonth" required>
                    <option value="monthend">Month end</option>
                </select>
            </div>
        </div>

        <div id="weeklyOptions" class="frequency-options hidden">
            <div class="form-group">
                <label for="lastDayOfPeriod">Last day of period *</label>
                <select id="lastDayOfPeriod" name="lastDayOfPeriod" required>
                    <option value="monday">Monday</option>
                    <option value="tuesday">Tuesday</option>
                    <option value="wednesday">Wednesday</option>
                    <option value="thursday">Thursday</option>
                    <option value="friday">Friday</option>
                </select>
            </div>
        </div>

        <div id="biWeeklyOptions" class="frequency-options hidden">
            <div class="form-group">
                <label for="biWeeklyLastDay">Last day of month *</label>
                <select id="biWeeklyLastDay" name="biWeeklyLastDay" required>
                    <option value="monthend">Month end</option>
                </select>
            </div>
            <div class="form-group">
                <label for="biWeeklyInterimDay">Interim day of month *</label>
                <select id="biWeeklyInterimDay" name="biWeeklyInterimDay" required>
                    <option value="15">15th</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <button type="submit">Submit</button>
        </div>
    </form>

    <div id="result"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const frequencySelect = document.getElementById("frequency");
            const lastDayOfMonthSelect = document.getElementById("lastDayOfMonth");
            const lastDayOfPeriodSelect = document.getElementById("lastDayOfPeriod");
            const biWeeklyLastDaySelect = document.getElementById("biWeeklyLastDay");
            const biWeeklyInterimDaySelect = document.getElementById("biWeeklyInterimDay");
            const monthlyOptions = document.getElementById("monthlyOptions");
            const weeklyOptions = document.getElementById("weeklyOptions");
            const biWeeklyOptions = document.getElementById("biWeeklyOptions");
            const form = document.getElementById("payFrequencyForm");
            const result = document.getElementById("result");

            // Populate monthly options
            function populateLastDayOfMonth() {
                lastDayOfMonthSelect.innerHTML = "";
                const option = document.createElement("option");
                option.value = "monthend";
                option.textContent = "Month end";
                lastDayOfMonthSelect.appendChild(option);
                for (let i = 1; i <= 31; i++) {
                    const option = document.createElement("option");
                    option.value = i;
                    option.textContent = i;
                    lastDayOfMonthSelect.appendChild(option);
                }
            }

            // Hide all frequency-specific options and remove required attributes
            function hideAllFrequencyOptions() {
                monthlyOptions.classList.add("hidden");
                weeklyOptions.classList.add("hidden");
                biWeeklyOptions.classList.add("hidden");
                
                // Remove required attribute from all hidden fields to prevent validation errors
                lastDayOfMonthSelect.removeAttribute('required');
                lastDayOfPeriodSelect.removeAttribute('required');
                biWeeklyLastDaySelect.removeAttribute('required');
                biWeeklyInterimDaySelect.removeAttribute('required');
            }

            // Handle frequency changes
            frequencySelect.addEventListener("change", function () {
                hideAllFrequencyOptions();

                if (this.value === "monthly") {
                    monthlyOptions.classList.remove("hidden");
                    lastDayOfMonthSelect.setAttribute('required', 'required');
                    populateLastDayOfMonth();
                } else if (this.value === "weekly") {
                    weeklyOptions.classList.remove("hidden");
                    lastDayOfPeriodSelect.setAttribute('required', 'required');
                } else if (this.value === "bi-weekly") {
                    biWeeklyOptions.classList.remove("hidden");
                    biWeeklyLastDaySelect.setAttribute('required', 'required');
                    biWeeklyInterimDaySelect.setAttribute('required', 'required');
                }
            });

            // Initialize form
            function initializeForm() {
                hideAllFrequencyOptions();
                
                const currentFrequency = frequencySelect.value;
                if (currentFrequency === "monthly") {
                    populateLastDayOfMonth();
                    monthlyOptions.classList.remove("hidden");
                    lastDayOfMonthSelect.setAttribute('required', 'required');
                } else if (currentFrequency === "weekly") {
                    weeklyOptions.classList.remove("hidden");
                    lastDayOfPeriodSelect.setAttribute('required', 'required');
                } else if (currentFrequency === "bi-weekly") {
                    biWeeklyOptions.classList.remove("hidden");
                    biWeeklyLastDaySelect.setAttribute('required', 'required');
                    biWeeklyInterimDaySelect.setAttribute('required', 'required');
                }
            }

            // Handle form submission
            form.addEventListener("submit", function(e) {
                e.preventDefault();
                
                try {
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData);
                    
                    result.innerHTML = `<div class="success">Form submitted successfully!<br>Data: ${JSON.stringify(data, null, 2)}</div>`;
                } catch (error) {
                    result.innerHTML = `<div class="error">Form validation error: ${error.message}</div>`;
                }
            });

            // Initialize the form
            initializeForm();
        });
    </script>
</body>
</html>
