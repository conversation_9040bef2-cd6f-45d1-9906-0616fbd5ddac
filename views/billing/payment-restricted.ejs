<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Account Restricted - Payment Overdue | <%= user.currentCompany.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/billing.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    
    <style>
        .restriction-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .restriction-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .restriction-header {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #fecaca;
        }
        
        .restriction-icon {
            width: 80px;
            height: 80px;
            background: #dc2626;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 15px -3px rgba(220, 38, 38, 0.3);
        }
        
        .restriction-icon i {
            font-size: 2.5rem;
            color: white;
        }
        
        .restriction-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #991b1b;
            margin-bottom: 0.5rem;
        }
        
        .restriction-subtitle {
            color: #7f1d1d;
            font-size: 1.125rem;
            margin: 0;
        }
        
        .restriction-content {
            padding: 2rem;
        }
        
        .invoice-details {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .invoice-details h3 {
            color: #1e293b;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .invoice-field {
            display: flex;
            flex-direction: column;
        }
        
        .invoice-field label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 0.25rem;
        }
        
        .invoice-field span {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .overdue-badge {
            background: #dc2626;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .info-section {
            margin-bottom: 2rem;
        }
        
        .info-section h2 {
            color: #1e293b;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .info-section p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .info-section ul {
            color: #64748b;
            line-height: 1.6;
            padding-left: 1.5rem;
        }
        
        .info-section li {
            margin-bottom: 0.5rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary:hover {
            background: #4f46e5;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-secondary {
            background: white;
            color: #64748b;
            padding: 0.75rem 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .btn-secondary:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
            transform: translateY(-1px);
        }
        
        @media (max-width: 640px) {
            .restriction-page {
                padding: 1rem;
            }
            
            .restriction-header {
                padding: 1.5rem;
            }
            
            .restriction-content {
                padding: 1.5rem;
            }
            
            .invoice-info {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="restriction-page">
        <div class="restriction-container">
            <div class="restriction-header">
                <div class="restriction-icon">
                    <i class="ph ph-warning-circle"></i>
                </div>
                <h1 class="restriction-title">Account Restricted</h1>
                <p class="restriction-subtitle">Payment Overdue - Read-Only Mode Active</p>
            </div>
            
            <div class="restriction-content">
                <% if (paymentRestriction && paymentRestriction.invoice) { %>
                <div class="invoice-details">
                    <h3>
                        <i class="ph ph-file-text"></i>
                        Overdue Invoice Details
                    </h3>
                    <div class="invoice-info">
                        <div class="invoice-field">
                            <label>Invoice Number</label>
                            <span><%= paymentRestriction.invoice.number %></span>
                        </div>
                        <div class="invoice-field">
                            <label>Amount Due</label>
                            <span>R<%= paymentRestriction.invoice.amount.toFixed(2) %></span>
                        </div>
                        <div class="invoice-field">
                            <label>Due Date</label>
                            <span><%= new Date(paymentRestriction.invoice.dueDate).toLocaleDateString('en-ZA') %></span>
                        </div>
                        <div class="invoice-field">
                            <label>Days Overdue</label>
                            <span><%= paymentRestriction.daysOverdue %> days</span>
                        </div>
                    </div>
                    <div class="overdue-badge">
                        <i class="ph ph-clock"></i>
                        <%= paymentRestriction.daysOverdue %> days overdue
                    </div>
                </div>
                <% } %>
                
                <div class="info-section">
                    <h2>What does this mean?</h2>
                    <p>Your PandaPayroll account has been placed in read-only mode because your payment is more than 60 days overdue. This is a temporary restriction to ensure service sustainability.</p>
                </div>
                
                <div class="info-section">
                    <h2>What can I do in read-only mode?</h2>
                    <p>While in read-only mode, you can:</p>
                    <ul>
                        <li>View all your company and employee information</li>
                        <li>Access reports and download existing data</li>
                        <li>View payslips and other generated documents</li>
                        <li>Export data for compliance purposes</li>
                    </ul>
                    
                    <p>However, you cannot:</p>
                    <ul>
                        <li>Create new leave requests</li>
                        <li>Add or modify employee information</li>
                        <li>Run payroll calculations or create new payslips</li>
                        <li>Process payments or create new documents</li>
                        <li>Make changes to company settings</li>
                    </ul>
                </div>
                
                <div class="info-section">
                    <h2>How to restore full access?</h2>
                    <p>To restore full functionality to your account, simply make the overdue payment. Once payment is processed, your account will be automatically restored within a few minutes.</p>
                </div>
                
                <div class="action-buttons">
                    <a href="/billing/preferences" class="btn-primary" data-payment-action="true">
                        <i class="ph ph-credit-card"></i>
                        Make Payment Now
                    </a>
                    <a href="/dashboard" class="btn-secondary">
                        <i class="ph ph-house"></i>
                        Continue in Read-Only Mode
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Payment Restriction Script -->
    <%- include('../partials/payment-restriction') %>
</body>
</html>
