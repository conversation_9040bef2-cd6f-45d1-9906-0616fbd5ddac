<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payroll Compliance Reminder</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #1e293b;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    
    .header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    
    .header .subtitle {
      margin: 8px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }
    
    .priority-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      margin: 15px 0 0 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .priority-critical {
      background-color: #fee2e2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
    
    .priority-high {
      background-color: #fef3c7;
      color: #d97706;
      border: 1px solid #fed7aa;
    }
    
    .priority-medium {
      background-color: #dbeafe;
      color: #2563eb;
      border: 1px solid #bfdbfe;
    }
    
    .priority-low {
      background-color: #f3f4f6;
      color: #6b7280;
      border: 1px solid #e5e7eb;
    }
    
    .content {
      padding: 30px;
    }
    
    .event-card {
      background-color: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .event-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 10px 0;
    }
    
    .event-details {
      display: grid;
      gap: 12px;
      margin: 15px 0;
    }
    
    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #e2e8f0;
    }
    
    .detail-row:last-child {
      border-bottom: none;
    }
    
    .detail-label {
      font-weight: 500;
      color: #64748b;
      font-size: 14px;
    }
    
    .detail-value {
      font-weight: 600;
      color: #1e293b;
      font-size: 14px;
    }
    
    .description {
      background-color: #ffffff;
      border-left: 4px solid #6366f1;
      padding: 15px;
      margin: 15px 0;
      border-radius: 0 6px 6px 0;
    }
    
    .cta-section {
      text-align: center;
      margin: 30px 0;
    }
    
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
      color: white;
      text-decoration: none;
      padding: 14px 28px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 16px;
      transition: transform 0.2s ease;
    }
    
    .cta-button:hover {
      transform: translateY(-1px);
    }
    
    .footer {
      background-color: #f8fafc;
      padding: 20px 30px;
      border-top: 1px solid #e2e8f0;
      text-align: center;
      font-size: 14px;
      color: #64748b;
    }
    
    .footer a {
      color: #6366f1;
      text-decoration: none;
    }
    
    .compliance-note {
      background-color: #fef7cd;
      border: 1px solid #fbbf24;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
    }
    
    .compliance-note .icon {
      font-size: 18px;
      margin-right: 8px;
    }
    
    .compliance-note .text {
      font-size: 14px;
      color: #92400e;
      font-weight: 500;
    }
    
    @media (max-width: 600px) {
      .container {
        margin: 10px;
        border-radius: 8px;
      }
      
      .header, .content, .footer {
        padding: 20px;
      }
      
      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1>📅 Payroll Compliance Reminder</h1>
      <div class="subtitle">PandaPayroll Automated Notification System</div>
      <div class="priority-badge priority-<%= event.priority %>">
        <%= priorityText %>
      </div>
    </div>

    <!-- Content -->
    <div class="content">
      <p>Hello <% if (recipientName && recipientName !== 'Team Member') { %><strong><%= recipientName %></strong><% } %>,</p>

      <p>This is an automated reminder for an upcoming payroll compliance deadline<% if (recipient && recipient.role) { %> that requires attention from <strong><%= recipient.role %></strong> personnel<% } %>:</p>

      <!-- Event Card -->
      <div class="event-card">
        <div class="event-title"><%= event.title %></div>
        
        <div class="event-details">
          <div class="detail-row">
            <span class="detail-label">📋 Event Type:</span>
            <span class="detail-value"><%= typeText %></span>
          </div>
          <div class="detail-row">
            <span class="detail-label">📅 Due Date:</span>
            <span class="detail-value"><%= eventDate %></span>
          </div>
          <div class="detail-row">
            <span class="detail-label">⏰ Time Remaining:</span>
            <span class="detail-value"><%= daysText.charAt(0).toUpperCase() + daysText.slice(1) %></span>
          </div>
          <div class="detail-row">
            <span class="detail-label">🏢 Company:</span>
            <span class="detail-value"><%= companyName %></span>
          </div>
          <div class="detail-row">
            <span class="detail-label">⚡ Priority:</span>
            <span class="detail-value"><%= event.priority.charAt(0).toUpperCase() + event.priority.slice(1) %></span>
          </div>
        </div>

        <% if (event.description) { %>
        <div class="description">
          <strong>📝 Details:</strong><br>
          <%= event.description %>
        </div>
        <% } %>
      </div>

      <!-- Compliance Note -->
      <% if (event.category === 'tax_compliance') { %>
      <div class="compliance-note">
        <span class="icon">⚠️</span>
        <span class="text">
          This is a SARS compliance requirement. Late submissions may result in penalties and interest charges.
        </span>
      </div>
      <% } %>

      <!-- Call to Action -->
      <div class="cta-section">
        <a href="<%= dashboardUrl %>" class="cta-button">
          📊 View in Dashboard
        </a>
      </div>

      <p>Please ensure this compliance requirement is completed on time to avoid any penalties or issues.</p>

      <p>If you have any questions or need assistance, please contact your payroll administrator or visit the dashboard for more details.</p>

      <% if (recipient && recipient.role) { %>
      <p style="font-size: 14px; color: #64748b; font-style: italic;">
        📧 You received this notification as a <strong><%= recipient.role %></strong> for <strong><%= companyName %></strong>.
      </p>
      <% } %>
    </div>

    <!-- Footer -->
    <div class="footer">
      <p>
        This is an automated reminder from <strong>PandaPayroll</strong><br>
        <a href="<%= dashboardUrl %>">Visit Dashboard</a> | 
        <a href="mailto:<%= process.env.EMAIL_USER %>">Contact Support</a>
      </p>
      <p style="margin-top: 15px; font-size: 12px; color: #9ca3af;">
        © <%= currentYear %> PandaPayroll. All rights reserved.<br>
        You received this email because you are assigned to payroll compliance events.
      </p>
    </div>
  </div>
</body>
</html>
