<!-- Payment Restriction Script - Only loaded when account is restricted due to overdue payment -->
<% if (typeof paymentRestriction !== 'undefined' && paymentRestriction && paymentRestriction.isRestricted) { %>
    <!-- Add data attribute to body for script to detect -->
    <script>
        document.body.setAttribute('data-payment-restricted', 'true');
        
        // Pass restriction details to script
        window.paymentRestrictionData = {
            type: '<%= paymentRestriction.type %>',
            daysOverdue: <%= paymentRestriction.daysOverdue %>,
            invoiceNumber: '<%= paymentRestriction.invoice.number %>',
            invoiceAmount: '<%= paymentRestriction.invoice.amount.toFixed(2) %>',
            invoiceDueDate: '<%= paymentRestriction.invoice.dueDate %>',
            reason: '<%= paymentRestriction.reason %>'
        };
    </script>
    
    <!-- Load payment restriction script -->
    <script src="/js/payment-restriction.js"></script>
<% } %>
