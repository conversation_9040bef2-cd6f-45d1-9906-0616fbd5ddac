<%
/**
 * Enhanced Calculator Partial
 * 
 * This partial provides enhanced calculator display alongside the existing calculator.
 * It's designed to be completely safe and non-disruptive:
 * - No modification of existing calculations
 * - Graceful fallback to existing display if enhancement fails
 * - Preserves all South African tax compliance features
 * - Can be easily enabled/disabled via feature flag
 */

// Feature flag - can be easily disabled if needed
const enableEnhancedCalculator = true;

// Initialize enhanced calculator data
let enhancedData = null;
if (enableEnhancedCalculator) {
  try {
    const CalculatorEnhancer = require('../../utils/calculatorEnhancer');
    const enhancer = new CalculatorEnhancer();
    enhancedData = enhancer.processCalculatorData(payroll, employee, currentPeriod);
  } catch (error) {
    console.warn('Enhanced calculator failed, using fallback:', error.message);
    enhancedData = null;
  }
}
%>

<% if (enhancedData && enhancedData.metadata.enhanced) { %>
<!-- Enhanced Calculator Display -->
<div class="enhanced-calculator" style="margin-top: 1rem; padding: 1rem; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
  <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
    <i class="ph ph-sparkle" style="color: #6366f1;"></i>
    <h4 style="margin: 0; color: #1e293b; font-size: 0.875rem; font-weight: 600;">Enhanced View</h4>
    <span style="background: #10b981; color: white; padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">BETA</span>
  </div>

  <div class="enhanced-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
    
    <!-- Income Components -->
    <% if (enhancedData.categories.income.length > 0) { %>
    <div class="enhanced-section">
      <h5 style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem; color: #10b981; font-size: 0.875rem; font-weight: 600;">
        <i class="ph ph-money"></i>
        Income
      </h5>
      <% enhancedData.categories.income.forEach(component => { %>
      <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e2e8f0;">
        <span style="font-size: 0.875rem; color: #64748b;"><%= component.name %></span>
        <span style="font-size: 0.875rem; font-weight: 600; color: #1e293b;"><%= component.formatted %></span>
      </div>
      <% }); %>
    </div>
    <% } %>

    <!-- Allowance Components -->
    <% if (enhancedData.categories.allowances.length > 0) { %>
    <div class="enhanced-section">
      <h5 style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem; color: #3b82f6; font-size: 0.875rem; font-weight: 600;">
        <i class="ph ph-car"></i>
        Allowances
      </h5>
      <% enhancedData.categories.allowances.forEach(component => { %>
      <div style="padding: 0.5rem 0; border-bottom: 1px solid #e2e8f0;">
        <div style="display: flex; justify-content: space-between;">
          <span style="font-size: 0.875rem; color: #64748b;"><%= component.name %></span>
          <span style="font-size: 0.875rem; font-weight: 600; color: #1e293b;"><%= component.formatted %></span>
        </div>
        <% if (component.breakdown && component.breakdown.taxable !== undefined) { %>
        <div style="margin-top: 0.25rem; font-size: 0.75rem; color: #64748b;">
          Taxable: R <%= component.breakdown.taxable.toFixed(2) %> | Non-taxable: R <%= component.breakdown.nonTaxable.toFixed(2) %>
        </div>
        <% } %>
      </div>
      <% }); %>
    </div>
    <% } %>

    <!-- Benefit Components -->
    <% if (enhancedData.categories.benefits.length > 0) { %>
    <div class="enhanced-section">
      <h5 style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem; color: #8b5cf6; font-size: 0.875rem; font-weight: 600;">
        <i class="ph ph-heart"></i>
        Benefits
      </h5>
      <% enhancedData.categories.benefits.forEach(component => { %>
      <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e2e8f0;">
        <span style="font-size: 0.875rem; color: #64748b;"><%= component.name %></span>
        <span style="font-size: 0.875rem; font-weight: 600; color: #1e293b;"><%= component.formatted %></span>
      </div>
      <% }); %>
    </div>
    <% } %>

    <!-- Deduction Components -->
    <% if (enhancedData.categories.deductions.length > 0) { %>
    <div class="enhanced-section">
      <h5 style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem; color: #ef4444; font-size: 0.875rem; font-weight: 600;">
        <i class="ph ph-minus-circle"></i>
        Deductions
      </h5>
      <% enhancedData.categories.deductions.forEach(component => { %>
      <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e2e8f0;">
        <span style="font-size: 0.875rem; color: #64748b;"><%= component.name %></span>
        <span style="font-size: 0.875rem; font-weight: 600; color: #1e293b;"><%= component.formatted %></span>
      </div>
      <% }); %>
    </div>
    <% } %>

  </div>

  <!-- Enhanced Summary -->
  <div style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 6px; border: 1px solid #e2e8f0;">
    <h5 style="margin-bottom: 0.75rem; color: #1e293b; font-size: 0.875rem; font-weight: 600;">Summary</h5>
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">
      <div style="text-align: center;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Gross Pay</div>
        <div style="font-size: 1rem; font-weight: 600; color: #10b981;">R <%= enhancedData.summary.grossPay.toFixed(2) %></div>
      </div>
      <div style="text-align: center;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Deductions</div>
        <div style="font-size: 1rem; font-weight: 600; color: #ef4444;">R <%= enhancedData.summary.totalDeductions.toFixed(2) %></div>
      </div>
      <div style="text-align: center;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Net Pay</div>
        <div style="font-size: 1rem; font-weight: 600; color: #1e293b;">R <%= enhancedData.summary.netPay.toFixed(2) %></div>
      </div>
    </div>
  </div>

  <!-- Data Source Indicator -->
  <div style="margin-top: 0.5rem; font-size: 0.75rem; color: #64748b; text-align: center;">
    Data source: <%= enhancedData.summary.source %> | Enhanced at: <%= new Date(enhancedData.metadata.timestamp).toLocaleTimeString() %>
  </div>
</div>

<% } else { %>
<!-- Fallback: Show that enhancement is available but not active -->
<div style="margin-top: 1rem; padding: 0.75rem; background: #fef3c7; border-radius: 6px; border: 1px solid #f59e0b;">
  <div style="display: flex; align-items: center; gap: 0.5rem;">
    <i class="ph ph-info" style="color: #f59e0b;"></i>
    <span style="font-size: 0.875rem; color: #92400e;">Enhanced calculator view is available but not currently active.</span>
  </div>
</div>
<% } %>

<script>
// Enhanced calculator JavaScript (if needed)
document.addEventListener('DOMContentLoaded', function() {
  // Add any interactive features for the enhanced calculator
  const enhancedCalculator = document.querySelector('.enhanced-calculator');
  if (enhancedCalculator) {
    console.log('Enhanced calculator loaded successfully');
    
    // Add click handlers for component details if needed
    enhancedCalculator.addEventListener('click', function(e) {
      if (e.target.closest('.enhanced-section')) {
        // Could add modal or expanded view functionality here
      }
    });
  }
});
</script>
