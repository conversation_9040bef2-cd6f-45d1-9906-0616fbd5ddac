<%
/**
 * Enhanced Travel Allowance Calculator Partial
 * 
 * This partial provides enhanced travel allowance display alongside the existing calculator.
 * SAFETY FEATURES:
 * - No modification of existing calculations
 * - Graceful fallback to existing display if enhancement fails
 * - Preserves all South African tax compliance features
 * - Feature flag controlled for easy disable
 */

// Feature flag - can be easily disabled if needed
const enableEnhancedTravelAllowance = true;

// Initialize enhanced travel allowance data
let enhancedTravelData = null;
let enhancedCalculatorData = null;

if (enableEnhancedTravelAllowance) {
  try {
    const CalculatorEnhancer = require('../../utils/calculatorEnhancer');
    const enhancer = new CalculatorEnhancer();
    
    // Process full calculator data
    enhancedCalculatorData = enhancer.processCalculatorData(payroll, employee, currentPeriod);
    
    // Get specific travel allowance enhancement
    enhancedTravelData = enhancer.processTravelAllowanceEnhanced(payroll);
    
  } catch (error) {
    console.warn('Enhanced travel allowance calculator failed, using fallback:', error.message);
    enhancedTravelData = null;
    enhancedCalculatorData = null;
  }
}

// Get existing travel allowance data for comparison
const existingTravelAllowance = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
const hasExistingTravelAllowance = existingTravelAllowance > 0;
%>

<% if (enhancedTravelData && hasExistingTravelAllowance) { %>
<!-- Enhanced Travel Allowance Calculator -->
<div class="enhanced-travel-allowance" style="margin-top: 1.5rem; padding: 1.5rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 12px; border: 2px solid #3b82f6; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
  
  <!-- Header -->
  <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1.5rem;">
    <div style="display: flex; align-items: center; gap: 0.75rem;">
      <div style="background: #3b82f6; color: white; padding: 0.5rem; border-radius: 8px;">
        <i class="ph ph-car" style="font-size: 1.25rem;"></i>
      </div>
      <div>
        <h4 style="margin: 0; color: #1e293b; font-size: 1.125rem; font-weight: 700;">Enhanced Travel Allowance Calculator</h4>
        <p style="margin: 0; color: #64748b; font-size: 0.875rem;">Detailed breakdown with tax implications</p>
      </div>
    </div>
    <div style="display: flex; align-items: center; gap: 0.5rem;">
      <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 16px; font-size: 0.75rem; font-weight: 600;">LIVE</span>
      <span style="background: #f59e0b; color: white; padding: 0.25rem 0.75rem; border-radius: 16px; font-size: 0.75rem; font-weight: 600;">BETA</span>
    </div>
  </div>

  <!-- Main Travel Allowance Display -->
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
    
    <!-- Total Allowance -->
    <div style="background: white; padding: 1.25rem; border-radius: 10px; border: 1px solid #e2e8f0; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
      <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;">
        <i class="ph ph-money" style="color: #3b82f6; font-size: 1.125rem;"></i>
        <h5 style="margin: 0; color: #1e293b; font-size: 1rem; font-weight: 600;">Total Allowance</h5>
      </div>
      <div style="font-size: 1.75rem; font-weight: 700; color: #1e293b; margin-bottom: 0.5rem;">
        <%= enhancedTravelData.formatted.total %>
      </div>
      <div style="font-size: 0.875rem; color: #64748b;">
        Monthly travel allowance amount
      </div>
    </div>

    <!-- Tax Treatment -->
    <div style="background: white; padding: 1.25rem; border-radius: 10px; border: 1px solid #e2e8f0; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
      <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;">
        <i class="ph ph-scales" style="color: #8b5cf6; font-size: 1.125rem;"></i>
        <h5 style="margin: 0; color: #1e293b; font-size: 1rem; font-weight: 600;">Tax Treatment</h5>
      </div>
      <div style="font-size: 1.125rem; font-weight: 600; color: #8b5cf6; margin-bottom: 0.5rem;">
        <%= enhancedTravelData.taxablePercentage %>% Taxable / <%= enhancedTravelData.nonTaxablePercentage %>% Non-taxable
      </div>
      <div style="font-size: 0.875rem; color: #64748b;">
        <% if (enhancedTravelData.only20PercentTax) { %>
          Special 20% tax rate applied
        <% } else { %>
          Standard 80/20 split applied
        <% } %>
      </div>
    </div>
  </div>

  <!-- Detailed Breakdown -->
  <div style="background: white; padding: 1.5rem; border-radius: 10px; border: 1px solid #e2e8f0; margin-bottom: 1.5rem;">
    <h5 style="margin-bottom: 1rem; color: #1e293b; font-size: 1rem; font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
      <i class="ph ph-chart-pie" style="color: #6366f1;"></i>
      Breakdown & Tax Impact
    </h5>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
      
      <!-- Taxable Portion -->
      <div style="padding: 1rem; background: #fef2f2; border-radius: 8px; border-left: 4px solid #ef4444;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
          <span style="font-weight: 600; color: #dc2626;">Taxable Portion</span>
          <span style="background: #dc2626; color: white; padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">
            <%= enhancedTravelData.taxablePercentage %>%
          </span>
        </div>
        <div style="font-size: 1.5rem; font-weight: 700; color: #dc2626; margin-bottom: 0.5rem;">
          <%= enhancedTravelData.formatted.taxable %>
        </div>
        <div style="font-size: 0.875rem; color: #7f1d1d;">
          Added to taxable income for PAYE calculation
        </div>
      </div>

      <!-- Non-taxable Portion -->
      <div style="padding: 1rem; background: #f0fdf4; border-radius: 8px; border-left: 4px solid #22c55e;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
          <span style="font-weight: 600; color: #16a34a;">Non-taxable Portion</span>
          <span style="background: #16a34a; color: white; padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">
            <%= enhancedTravelData.nonTaxablePercentage %>%
          </span>
        </div>
        <div style="font-size: 1.5rem; font-weight: 700; color: #16a34a; margin-bottom: 0.5rem;">
          <%= enhancedTravelData.formatted.nonTaxable %>
        </div>
        <div style="font-size: 0.875rem; color: #14532d;">
          Excluded from taxable income
        </div>
      </div>
    </div>
  </div>

  <!-- Impact on Calculations -->
  <% if (enhancedCalculatorData && enhancedCalculatorData.summary) { %>
  <div style="background: #f8fafc; padding: 1.25rem; border-radius: 10px; border: 1px solid #e2e8f0; margin-bottom: 1rem;">
    <h5 style="margin-bottom: 1rem; color: #1e293b; font-size: 1rem; font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
      <i class="ph ph-calculator" style="color: #6366f1;"></i>
      Integrated Payroll Calculation Impact
    </h5>

    <!-- Main Summary Grid -->
    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; text-align: center; margin-bottom: 1.5rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e2e8f0;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Gross Pay</div>
        <div style="font-size: 1.25rem; font-weight: 700; color: #059669;">
          R <%= enhancedCalculatorData.summary.grossPay.toFixed(2) %>
        </div>
        <div style="font-size: 0.75rem; color: #64748b; margin-top: 0.25rem;">
          Includes full travel allowance
        </div>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e2e8f0;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Taxable Income</div>
        <div style="font-size: 1.25rem; font-weight: 700; color: #f59e0b;">
          R <%= enhancedCalculatorData.summary.breakdown.totalTaxableIncome.toFixed(2) %>
        </div>
        <div style="font-size: 0.75rem; color: #64748b; margin-top: 0.25rem;">
          For PAYE calculation
        </div>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e2e8f0;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Total Deductions</div>
        <div style="font-size: 1.25rem; font-weight: 700; color: #ef4444;">
          R <%= enhancedCalculatorData.summary.totalDeductions.toFixed(2) %>
        </div>
        <div style="font-size: 0.75rem; color: #64748b; margin-top: 0.25rem;">
          Including travel allowance tax
        </div>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e2e8f0;">
        <div style="font-size: 0.75rem; color: #64748b; margin-bottom: 0.25rem;">Net Pay</div>
        <div style="font-size: 1.25rem; font-weight: 700; color: #1e293b;">
          R <%= enhancedCalculatorData.summary.netPay.toFixed(2) %>
        </div>
        <div style="font-size: 0.75rem; color: #64748b; margin-top: 0.25rem;">
          After all deductions
        </div>
      </div>
    </div>

    <!-- Travel Allowance Tax Impact -->
    <% if (enhancedCalculatorData.summary.breakdown.deductions.travelAllowanceImpact) { %>
    <div style="background: #fef3c7; padding: 1rem; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 1rem;">
      <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
        <i class="ph ph-info" style="color: #f59e0b;"></i>
        <span style="font-weight: 600; color: #92400e;">Travel Allowance Tax Impact</span>
      </div>
      <div style="font-size: 0.875rem; color: #92400e;">
        Additional PAYE due to taxable portion:
        <strong>R <%= enhancedCalculatorData.summary.breakdown.deductions.travelAllowanceImpact.additionalPAYE.toFixed(2) %></strong>
      </div>
    </div>
    <% } %>

    <!-- Detailed Deductions Breakdown -->
    <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e2e8f0;">
      <h6 style="margin-bottom: 0.75rem; color: #1e293b; font-size: 0.875rem; font-weight: 600;">Enhanced Deductions Breakdown</h6>
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.75rem; font-size: 0.875rem;">
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #64748b;">PAYE Tax:</span>
          <span style="font-weight: 600; color: #1e293b;">R <%= enhancedCalculatorData.summary.breakdown.deductions.paye.toFixed(2) %></span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #64748b;">UIF:</span>
          <span style="font-weight: 600; color: #1e293b;">R <%= enhancedCalculatorData.summary.breakdown.deductions.uif.toFixed(2) %></span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #64748b;">SDL:</span>
          <span style="font-weight: 600; color: #1e293b;">R <%= enhancedCalculatorData.summary.breakdown.deductions.sdl.toFixed(2) %></span>
        </div>
        <% if (enhancedCalculatorData.summary.breakdown.deductions.medicalAidEmployee > 0) { %>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #64748b;">Medical Aid:</span>
          <span style="font-weight: 600; color: #1e293b;">R <%= enhancedCalculatorData.summary.breakdown.deductions.medicalAidEmployee.toFixed(2) %></span>
        </div>
        <% } %>
        <% if (enhancedCalculatorData.summary.breakdown.deductions.garnishee > 0) { %>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #64748b;">Garnishee:</span>
          <span style="font-weight: 600; color: #1e293b;">R <%= enhancedCalculatorData.summary.breakdown.deductions.garnishee.toFixed(2) %></span>
        </div>
        <% } %>
      </div>
    </div>
  </div>
  <% } %>

  <!-- Pay Component Status Check -->
  <div style="background: #fffbeb; padding: 1rem; border-radius: 8px; border: 1px solid #f59e0b; margin-bottom: 1rem;">
    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
      <i class="ph ph-info" style="color: #f59e0b;"></i>
      <span style="font-weight: 600; color: #92400e;">Pay Component Status</span>
    </div>
    <div style="font-size: 0.875rem; color: #92400e;">
      <div style="margin-bottom: 0.25rem;">
        Travel Allowance:
        <strong>
          <% if (Math.abs(enhancedTravelData.totalAmount - existingTravelAllowance) < 0.01) { %>
            ✅ Verified (R <%= existingTravelAllowance.toFixed(2) %>)
          <% } else { %>
            ⚠️ Difference detected (Existing: R <%= existingTravelAllowance.toFixed(2) %>, Enhanced: <%= enhancedTravelData.formatted.total %>)
          <% } %>
        </strong>
      </div>
      <div>
        Accommodation Benefit:
        <strong>
          <% if (payroll?.accommodationBenefit > 0) { %>
            ✅ Active (R <%= payroll.accommodationBenefit.toFixed(2) %>)
          <% } else { %>
            ⚪ Not set or removed
          <% } %>
        </strong>
      </div>
    </div>
  </div>

  <!-- Data Source & Timestamp -->
  <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e2e8f0; font-size: 0.75rem; color: #64748b; text-align: center;">
    <div>
      Data source: <%= enhancedCalculatorData?.summary?.source || 'enhanced' %> | 
      Enhanced at: <%= new Date().toLocaleTimeString() %> | 
      Employee: <%= employee.firstName %> <%= employee.lastName %>
    </div>
  </div>
</div>

<% } else if (hasExistingTravelAllowance && !enhancedTravelData) { %>
<!-- Fallback: Show that enhancement is available but failed -->
<div style="margin-top: 1rem; padding: 1rem; background: #fef3c7; border-radius: 8px; border: 1px solid #f59e0b;">
  <div style="display: flex; align-items: center; gap: 0.5rem;">
    <i class="ph ph-warning" style="color: #f59e0b;"></i>
    <span style="font-size: 0.875rem; color: #92400e;">
      Enhanced travel allowance calculator is available but not currently active. 
      Existing travel allowance: R <%= existingTravelAllowance.toFixed(2) %>
    </span>
  </div>
</div>

<% } else if (!hasExistingTravelAllowance) { %>
<!-- No travel allowance message -->
<div style="margin-top: 1rem; padding: 1rem; background: #f1f5f9; border-radius: 8px; border: 1px solid #cbd5e1;">
  <div style="display: flex; align-items: center; gap: 0.5rem;">
    <i class="ph ph-info" style="color: #64748b;"></i>
    <span style="font-size: 0.875rem; color: #475569;">
      No travel allowance configured for this employee.
    </span>
  </div>
</div>
<% } %>

<script>
// Enhanced travel allowance calculator JavaScript
document.addEventListener('DOMContentLoaded', function() {
  const enhancedTravelCalculator = document.querySelector('.enhanced-travel-allowance');
  if (enhancedTravelCalculator) {
    console.log('Enhanced travel allowance calculator loaded successfully');
    
    // Add any interactive features
    enhancedTravelCalculator.addEventListener('click', function(e) {
      // Could add modal or expanded view functionality here
      if (e.target.closest('.breakdown-section')) {
        console.log('Travel allowance breakdown clicked');
      }
    });
  }
});
</script>
