# PandaPayroll Compliance Calendar - Database Model Documentation

## Primary Model: PayrollCalendarEvent

**File Location:** `/models/PayrollCalendarEvent.js`

### Schema Structure

The PayrollCalendarEvent model is the primary database model used to store all compliance events in the PandaPayroll system.

#### Core Fields

```javascript
{
  title: String (required) - Event title/name
  description: String - Detailed event description
  date: Date (required) - Event due date
  type: String (enum) - Event type classification
  category: String (enum) - Event category
  priority: String (enum) - Event priority level
  status: String (enum) - Current event status
  isRecurring: Boolean - Whether event repeats
  company: ObjectId (required) - Reference to Company model
  createdBy: ObjectId (required) - Reference to User model
  assignedTo: [ObjectId] - Array of User references
}
```

#### Event Types (Enum Values)

- `cutoff` - Payroll cut-off dates
- `processing` - Payroll processing deadlines  
- `payment` - Payment dates
- `emp201` - EMP201 submission deadlines
- `emp501` - EMP501 reconciliation deadlines
- `irp5` - IRP5 submission deadlines
- `it3a` - IT3a submission deadlines
- `uif` - UIF submissions
- `sdl` - SDL submissions
- `paye` - PAYE submissions
- `eti` - Employment Tax Incentive
- `custom` - Custom payroll events
- `reminder` - General reminders
- `compliance` - Other compliance deadlines

#### Categories (Enum Values)

- `payroll` - Payroll-related events
- `tax_compliance` - Tax compliance requirements
- `statutory` - Statutory compliance
- `reporting` - Reporting requirements
- `administrative` - Administrative tasks

#### Priority Levels (Enum Values)

- `low` - Low priority
- `medium` - Medium priority (default)
- `high` - High priority
- `critical` - Critical priority

#### Status Values (Enum Values)

- `pending` - Not yet started (default)
- `in_progress` - Currently being worked on
- `completed` - Successfully completed
- `overdue` - Past due date and not completed

#### Email Reminder System

```javascript
emailReminders: [{
  daysBeforeEvent: Number (required) - Days before event when reminder was sent
  sentAt: Date (required) - Timestamp when reminder was sent
  sentTo: [{
    email: String - Recipient email address
    userId: ObjectId - Reference to User model
  }]
  emailStatus: String (enum: 'sent', 'failed', 'bounced') - Delivery status
  messageId: String - Email message ID for tracking
  errorMessage: String - Error details if delivery failed
}]
```

#### Reminder Settings

```javascript
reminderSettings: {
  enabled: Boolean (default: true) - Whether reminders are enabled
  daysBeforeEvent: [Number] (default: [7, 3, 1]) - Days before event to send reminders
  emailReminder: Boolean (default: true) - Enable email reminders
  dashboardReminder: Boolean (default: true) - Enable dashboard notifications
}
```

#### Recurrence Pattern

```javascript
recurrencePattern: {
  frequency: String (enum: 'daily', 'weekly', 'monthly', 'quarterly', 'yearly')
  interval: Number (default: 1) - Frequency interval
  dayOfMonth: Number (1-31) - Specific day of month for monthly/yearly
  dayOfWeek: Number (0-6) - Day of week for weekly (0=Sunday)
  endDate: Date - When recurrence ends
}
```

#### Metadata

```javascript
metadata: {
  source: String (enum: 'manual', 'system_generated', 'imported', 'comprehensive_compliance')
  tags: [String] - Searchable tags
  customFields: Mixed - Additional custom data
}
```

### Related Models

#### User Model
**File Location:** `/models/user.js`

Key fields for email reminders:
- `email: String (required, unique)` - Primary email address
- `firstName: String` - User's first name
- `lastName: String` - User's last name
- `companies: [ObjectId]` - Array of company references
- `currentCompany: ObjectId` - Active company reference
- `roleName: String` - User role (owner, admin, manager, etc.)

#### Company Model
**File Location:** `/models/Company.js`

Key fields:
- `name: String (required)` - Company name
- `companyCode: String (required, unique)` - Unique company identifier
- `owner: ObjectId (required)` - Reference to User model
- `employees: [ObjectId]` - Array of Employee references

### Database Indexes

The PayrollCalendarEvent model includes several indexes for performance:

- `company + type` - For filtering events by company and type
- `company + status` - For filtering by company and status
- `date + status` - For finding upcoming/overdue events
- `assignedTo + status` - For user-specific event queries

### Static Methods

#### createComprehensiveComplianceEvents(companyId, year)
Creates a full set of South African compliance events including:
- Monthly EMP201 submissions (due 7th of each month)
- Annual EMP501 reconciliation (due end of May)
- Annual IRP5/IT3a submissions (due end of May)
- Quarterly UIF reviews
- Annual SDL submissions
- ETI compliance deadlines
- Year-end preparation tasks

### Usage in Email Reminder System

The email reminder system queries this model to:

1. **Find pending reminders:** Events where current date matches reminder schedule
2. **Track sent reminders:** Prevent duplicate emails using `emailReminders` array
3. **Determine recipients:** Use `assignedTo` field or fallback to company administrators
4. **Record delivery status:** Store email delivery results for monitoring

### Data Flow

1. **Event Creation:** Events created manually or via compliance initialization
2. **Reminder Processing:** Daily scheduler checks for pending reminders
3. **Email Generation:** System generates context-aware emails based on event data
4. **Delivery Tracking:** Email delivery status recorded in `emailReminders` array
5. **Status Updates:** Events marked as completed when tasks are finished

This model provides comprehensive tracking of payroll compliance events with robust email reminder capabilities and detailed audit trails.
