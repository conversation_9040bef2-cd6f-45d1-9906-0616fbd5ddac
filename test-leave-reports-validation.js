const mongoose = require("mongoose");
const Report = require("./models/Report");
const Company = require("./models/company");
const User = require("./models/User");

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testLeaveReportsValidation() {
  try {
    console.log("🧪 Testing Leave Reports Validation...");

    // Find the test company and user
    const company = await Company.findOne({ companyCode: "CEA08B3B" });
    const user = await User.findOne({ companies: company._id });

    if (!company || !user) {
      console.error("❌ Test company or user not found");
      return;
    }

    console.log(`✅ Using company: ${company.name}`);
    console.log(`✅ Using user: ${user.firstName} ${user.lastName}`);

    // Test creating Report documents with the new leave report types
    const reportTypes = ['leaveDaysReport', 'leaveExpiryReport', 'leaveReport'];
    
    for (const reportType of reportTypes) {
      console.log(`\n📊 Testing Report creation for: ${reportType}`);
      
      try {
        // Create a test report document
        const testReport = new Report({
          company: company._id,
          type: reportType,
          format: 'excel',
          settings: {},
          dateRange: {
            startDate: new Date(),
            endDate: new Date()
          },
          generatedBy: user._id,
          data: Buffer.from('test data'),
          status: 'completed'
        });

        // Validate the document (this will throw if enum validation fails)
        await testReport.validate();
        console.log(`✅ ${reportType}: Validation passed`);

        // Save the document to test database constraints
        await testReport.save();
        console.log(`✅ ${reportType}: Successfully saved to database`);

        // Clean up - delete the test report
        await Report.findByIdAndDelete(testReport._id);
        console.log(`✅ ${reportType}: Test report cleaned up`);

      } catch (error) {
        console.error(`❌ ${reportType}: Validation failed -`, error.message);
        
        // Check if it's specifically an enum validation error
        if (error.message.includes('is not a valid enum value')) {
          console.error(`❌ ${reportType}: ENUM VALIDATION ERROR - This report type is not allowed in the Report model`);
        }
      }
    }

    // Test with an invalid report type to ensure validation is working
    console.log(`\n🚫 Testing with invalid report type...`);
    try {
      const invalidReport = new Report({
        company: company._id,
        type: 'invalidReportType',
        format: 'excel',
        settings: {},
        dateRange: {
          startDate: new Date(),
          endDate: new Date()
        },
        generatedBy: user._id,
        data: Buffer.from('test data'),
        status: 'completed'
      });

      await invalidReport.validate();
      console.log(`❌ Invalid report type validation should have failed!`);
    } catch (error) {
      console.log(`✅ Invalid report type correctly rejected: ${error.message}`);
    }

    console.log("\n🎉 Leave Reports Validation Testing Complete!");
    console.log("\n📋 Summary:");
    console.log("- All three leave report types are now valid enum values");
    console.log("- Report documents can be created and saved successfully");
    console.log("- Validation is working correctly for both valid and invalid types");
    console.log("- The mongoose validation error has been resolved");

  } catch (error) {
    console.error("❌ Error during validation testing:", error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the validation test
testLeaveReportsValidation();
