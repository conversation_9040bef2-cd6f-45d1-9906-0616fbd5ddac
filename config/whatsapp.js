module.exports = {
  // WhatsApp Business API Configuration
  api: {
    version: process.env.WHATSAPP_API_VERSION || 'v17.0',
    baseUrl: 'https://graph.facebook.com',
    phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID,
    accessToken: process.env.WHATSAPP_ACCESS_TOKEN,
    verifyToken: process.env.WHATSAPP_VERIFY_TOKEN,
  },

  // Session Management
  session: {
    expiryHours: 24, // Session duration in hours
    redisConfig: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
    },
  },

  // Message Templates
  templates: {
    welcome: 'Welcome to Panda Payroll! How can I help you today?\n\n' +
            '1. Request Payslip\n' +
            '2. Request IRP5\n' +
            '3. Apply for Leave\n\n' +
            'Reply with the number of your choice.',
    
    notRegistered: 'Your phone number is not registered in our system. ' +
                   'Please contact your HR department to update your contact details.',
    
    help: 'Please choose from the following options:\n\n' +
          '1. To request a payslip, send "payslip" or "payslip YYYY-MM"\n' +
          '2. To request an IRP5, send "irp5" or "irp5 YYYY"\n' +
          '3. To apply for leave, send "Leave request: [type] from YYYY-MM-DD to YYYY-MM-DD reason: [your reason]"\n\n' +
          'Example: "Leave request: Annual from 2024-03-01 to 2024-03-05 reason: Family vacation"',
    
    error: 'Sorry, there was an error processing your request. Please try again later.',
  },

  // Document Generation
  documents: {
    tempDir: process.env.TEMP_DIR || '/tmp/whatsapp-docs',
    maxSizeMB: 16, // WhatsApp's document size limit
    allowedTypes: ['application/pdf'],
  },

  // Rate Limiting
  rateLimit: {
    maxRequests: 20, // Maximum requests per window
    windowMs: 60000, // Time window in milliseconds (1 minute)
  },

  // Security
  security: {
    allowedCountryCodes: ['27'], // South Africa
    ipWhitelist: process.env.WHATSAPP_IP_WHITELIST?.split(',') || [],
  },
}; 