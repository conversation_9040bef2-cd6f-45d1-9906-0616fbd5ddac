/**
 * Test script for Leave Types Enhancement
 * Tests custom leave type functionality and dynamic UI updates
 */

require('dotenv').config();
const mongoose = require('mongoose');
const LeaveType = require('./models/LeaveType');
const Company = require('./models/Company');

async function testLeaveTypesEnhancement() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a test company
    let testCompany = await Company.findOne({ name: { $regex: /test/i } });
    if (!testCompany) {
      console.log('❌ No test company found. Please create a test company first.');
      return;
    }

    console.log(`📋 Using test company: ${testCompany.name} (${testCompany._id})`);

    // Test 1: Create a custom leave type
    console.log('\n🧪 Test 1: Creating Custom Leave Type');
    
    const customLeaveData = {
      company: testCompany._id,
      name: 'Study Leave',
      category: 'custom',
      description: 'Leave for educational purposes and professional development',
      daysPerYear: 5,
      accrualRate: 'yearly',
      paidLeave: true,
      requiresApproval: true,
      requiresDocument: false,
      minDaysNotice: 7,
      gender: 'all',
      createdBy: new mongoose.Types.ObjectId() // Mock user ID
    };

    try {
      const customLeaveType = new LeaveType(customLeaveData);
      await customLeaveType.save();
      console.log(`   ✅ Custom leave type created: ${customLeaveType.name} (${customLeaveType._id})`);
      
      // Test the leave type properties
      console.log(`   📊 Category: ${customLeaveType.category}`);
      console.log(`   📊 Name: ${customLeaveType.name}`);
      console.log(`   📊 Days per year: ${customLeaveType.daysPerYear}`);
      console.log(`   📊 Active: ${customLeaveType.active}`);
      
    } catch (error) {
      if (error.code === 11000) {
        console.log('   ⚠️  Custom leave type already exists (duplicate name)');
      } else {
        console.error(`   ❌ Error creating custom leave type: ${error.message}`);
      }
    }

    // Test 2: Create another custom leave type with different name
    console.log('\n🧪 Test 2: Creating Another Custom Leave Type');
    
    const customLeaveData2 = {
      company: testCompany._id,
      name: 'Compassionate Leave',
      category: 'custom',
      description: 'Leave for family emergencies and bereavement',
      daysPerYear: 3,
      accrualRate: 'yearly',
      paidLeave: true,
      requiresApproval: true,
      requiresDocument: true,
      minDaysNotice: 0,
      gender: 'all',
      createdBy: new mongoose.Types.ObjectId()
    };

    try {
      const customLeaveType2 = new LeaveType(customLeaveData2);
      await customLeaveType2.save();
      console.log(`   ✅ Second custom leave type created: ${customLeaveType2.name} (${customLeaveType2._id})`);
      
    } catch (error) {
      if (error.code === 11000) {
        console.log('   ⚠️  Second custom leave type already exists (duplicate name)');
      } else {
        console.error(`   ❌ Error creating second custom leave type: ${error.message}`);
      }
    }

    // Test 3: Test duplicate name validation
    console.log('\n🧪 Test 3: Testing Duplicate Name Validation');
    
    try {
      const duplicateLeaveType = new LeaveType({
        company: testCompany._id,
        name: 'Study Leave', // Same name as first custom leave type
        category: 'custom',
        description: 'Duplicate test',
        daysPerYear: 2,
        accrualRate: 'yearly',
        paidLeave: true,
        requiresApproval: true,
        createdBy: new mongoose.Types.ObjectId()
      });
      
      await duplicateLeaveType.save();
      console.log('   ❌ Duplicate validation failed - should not have saved');
      
    } catch (error) {
      if (error.code === 11000) {
        console.log('   ✅ Duplicate name validation working correctly');
      } else {
        console.error(`   ❌ Unexpected error: ${error.message}`);
      }
    }

    // Test 4: Retrieve all leave types for the company
    console.log('\n🧪 Test 4: Retrieving All Leave Types');
    
    const allLeaveTypes = await LeaveType.find({ company: testCompany._id }).sort({ name: 1 });
    console.log(`   📊 Found ${allLeaveTypes.length} leave types for company:`);
    
    allLeaveTypes.forEach((leaveType, index) => {
      console.log(`   ${index + 1}. ${leaveType.name} (${leaveType.category}) - ${leaveType.active ? 'Active' : 'Inactive'}`);
    });

    // Test 5: Test WhatsApp compatibility
    console.log('\n🧪 Test 5: Testing WhatsApp Integration Compatibility');
    
    const customLeaveTypes = allLeaveTypes.filter(lt => lt.category === 'custom');
    console.log(`   📱 Found ${customLeaveTypes.length} custom leave types for WhatsApp integration:`);
    
    customLeaveTypes.forEach(leaveType => {
      // Simulate WhatsApp service logic
      const normalizedName = leaveType.name.toLowerCase();
      let contextMessage = 'Your request is pending approval.'; // Default
      
      if (normalizedName.includes('sick')) {
        contextMessage = 'Take care of yourself and we hope you feel better soon! 🌟';
      } else if (normalizedName.includes('family') || normalizedName.includes('compassionate')) {
        contextMessage = 'We understand the importance of family time and supporting loved ones. 💙';
      }
      
      console.log(`   📱 ${leaveType.name}: "${contextMessage}"`);
    });

    console.log('\n✅ Leave Types Enhancement tests completed!');
    console.log('\n📋 Summary:');
    console.log('   - Custom leave type creation working');
    console.log('   - Duplicate name validation functional');
    console.log('   - WhatsApp integration compatibility maintained');
    console.log('   - Database operations successful');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  testLeaveTypesEnhancement();
}

module.exports = { testLeaveTypesEnhancement };
