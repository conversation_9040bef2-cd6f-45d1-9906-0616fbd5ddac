/**
 * Calculator Enhancer Utility
 * 
 * This utility enhances the calculator display without modifying existing calculations.
 * It works alongside the current system to provide better categorization and display.
 * 
 * SAFETY FEATURES:
 * - No modification of existing calculation logic
 * - Graceful fallbacks to existing display if enhancement fails
 * - Read-only operations on payroll data
 * - Preserves all South African tax compliance features
 */

const { 
  PAY_COMPONENT_REGISTRY, 
  getComponentsByCategory, 
  getSortedComponents, 
  hasValue 
} = require('../constants/payComponentRegistry');

/**
 * Enhanced calculator data processor
 * Processes payroll data for enhanced display while preserving existing functionality
 */
class CalculatorEnhancer {
  constructor() {
    this.fallbackToExisting = true; // Always enable fallback for safety
  }

  /**
   * Process payroll data for enhanced calculator display
   * @param {Object} payrollData - Complete payroll data object
   * @param {Object} employee - Employee data
   * @param {Object} currentPeriod - Current payroll period
   * @returns {Object} Enhanced calculator data
   */
  processCalculatorData(payrollData, employee, currentPeriod) {
    try {
      const enhanced = {
        // Preserve original data for fallback
        original: {
          payrollData,
          employee,
          currentPeriod
        },
        
        // Enhanced categorized data
        categories: {
          income: this.processIncomeComponents(payrollData),
          allowances: this.processAllowanceComponents(payrollData),
          benefits: this.processBenefitComponents(payrollData),
          deductions: this.processDeductionComponents(payrollData, currentPeriod)
        },

        // Enhanced Travel Allowance (specific focus for this integration)
        travelAllowanceEnhanced: this.processTravelAllowanceEnhanced(payrollData),
        
        // Summary calculations (read-only, no modification of existing logic)
        summary: this.calculateSummary(payrollData, currentPeriod),
        
        // Metadata
        metadata: {
          enhanced: true,
          timestamp: new Date().toISOString(),
          employeeId: employee?._id,
          periodId: currentPeriod?._id
        }
      };

      return enhanced;
    } catch (error) {
      console.warn('Calculator enhancement failed, using fallback:', error.message);
      return this.createFallbackData(payrollData, employee, currentPeriod);
    }
  }

  /**
   * Process income components
   * @param {Object} payrollData - Payroll data
   * @returns {Array} Processed income components
   */
  processIncomeComponents(payrollData) {
    const incomeConfig = getComponentsByCategory('INCOME');
    if (!incomeConfig) return [];

    const components = getSortedComponents('INCOME');
    const processedComponents = [];

    for (const component of components) {
      if (hasValue(payrollData, component.source)) {
        const value = this.getValueFromSource(payrollData, component.source);
        
        processedComponents.push({
          key: component.key,
          name: component.name,
          description: component.description,
          value: Number(value || 0),
          taxable: component.taxable,
          displayOrder: component.displayOrder,
          formatted: this.formatCurrency(value)
        });
      }
    }

    return processedComponents;
  }

  /**
   * Process allowance components
   * @param {Object} payrollData - Payroll data
   * @returns {Array} Processed allowance components
   */
  processAllowanceComponents(payrollData) {
    const allowanceConfig = getComponentsByCategory('ALLOWANCES');
    if (!allowanceConfig) return [];

    const components = getSortedComponents('ALLOWANCES');
    const processedComponents = [];

    for (const component of components) {
      if (hasValue(payrollData, component.source)) {
        const processed = {
          key: component.key,
          name: component.name,
          description: component.description,
          taxable: component.taxable,
          displayOrder: component.displayOrder
        };

        // Handle complex components with breakdowns
        if (component.breakdown) {
          processed.breakdown = this.processBreakdown(payrollData, component.breakdown);
          processed.value = processed.breakdown.total || 0;
        } else {
          const value = this.getValueFromSource(payrollData, component.source);
          processed.value = Number(value || 0);
        }

        processed.formatted = this.formatCurrency(processed.value);
        processedComponents.push(processed);
      }
    }

    return processedComponents;
  }

  /**
   * Enhanced Travel Allowance Processing
   * Specifically handles travel allowance with proper tax calculations
   * @param {Object} payrollData - Payroll data
   * @returns {Object} Enhanced travel allowance data
   */
  processTravelAllowanceEnhanced(payrollData) {
    const travelAllowance = payrollData?.travelAllowance;
    if (!travelAllowance || !travelAllowance.fixedAllowanceAmount) {
      return null;
    }

    const totalAmount = Number(travelAllowance.fixedAllowanceAmount || 0);
    if (totalAmount <= 0) return null;

    // Determine tax treatment based on existing logic
    const only20PercentTax = travelAllowance.only20PercentTax || false;

    // Calculate taxable and non-taxable portions
    const taxablePercentage = only20PercentTax ? 0.2 : 0.8;
    const nonTaxablePercentage = only20PercentTax ? 0.8 : 0.2;

    const taxableAmount = totalAmount * taxablePercentage;
    const nonTaxableAmount = totalAmount * nonTaxablePercentage;

    return {
      totalAmount: totalAmount,
      taxableAmount: taxableAmount,
      nonTaxableAmount: nonTaxableAmount,
      taxablePercentage: Math.round(taxablePercentage * 100),
      nonTaxablePercentage: Math.round(nonTaxablePercentage * 100),
      only20PercentTax: only20PercentTax,
      formatted: {
        total: this.formatCurrency(totalAmount),
        taxable: this.formatCurrency(taxableAmount),
        nonTaxable: this.formatCurrency(nonTaxableAmount)
      },
      // Additional metadata for integration
      impactOnPAYE: {
        additionalTaxableIncome: taxableAmount,
        description: `${Math.round(taxablePercentage * 100)}% of travel allowance is taxable`
      }
    };
  }

  /**
   * Process benefit components
   * @param {Object} payrollData - Payroll data
   * @returns {Array} Processed benefit components
   */
  processBenefitComponents(payrollData) {
    const benefitConfig = getComponentsByCategory('BENEFITS');
    if (!benefitConfig) return [];

    const components = getSortedComponents('BENEFITS');
    const processedComponents = [];

    for (const component of components) {
      if (hasValue(payrollData, component.source)) {
        const processed = {
          key: component.key,
          name: component.name,
          description: component.description,
          taxable: component.taxable,
          displayOrder: component.displayOrder
        };

        // Handle complex components with breakdowns
        if (component.breakdown) {
          processed.breakdown = this.processBreakdown(payrollData, component.breakdown);
          processed.value = processed.breakdown.total || 0;
        } else {
          const value = this.getValueFromSource(payrollData, component.source);
          processed.value = Number(value || 0);
        }

        processed.formatted = this.formatCurrency(processed.value);
        processedComponents.push(processed);
      }
    }

    return processedComponents;
  }

  /**
   * Process deduction components
   * @param {Object} payrollData - Payroll data
   * @param {Object} currentPeriod - Current period data
   * @returns {Array} Processed deduction components
   */
  processDeductionComponents(payrollData, currentPeriod) {
    const deductionConfig = getComponentsByCategory('DEDUCTIONS');
    if (!deductionConfig) return [];

    const components = getSortedComponents('DEDUCTIONS');
    const processedComponents = [];

    for (const component of components) {
      let value = 0;
      let source = component.source;

      // Handle calculated values from current period
      if (component.calculation === 'computed' && currentPeriod) {
        switch (component.key) {
          case 'paye':
            value = currentPeriod.PAYE || 0;
            break;
          case 'uif':
            value = currentPeriod.UIF || 0;
            break;
          case 'sdl':
            value = currentPeriod.SDL || 0;
            break;
        }
      } else {
        // Handle direct values from payroll data
        if (hasValue(payrollData, source)) {
          value = this.getValueFromSource(payrollData, source);
        }
      }

      if (value > 0) {
        processedComponents.push({
          key: component.key,
          name: component.name,
          description: component.description,
          value: Number(value),
          taxable: component.taxable,
          displayOrder: component.displayOrder,
          formatted: this.formatCurrency(value)
        });
      }
    }

    return processedComponents;
  }

  /**
   * Process component breakdown (for complex components)
   * @param {Object} payrollData - Payroll data
   * @param {Object} breakdown - Breakdown configuration
   * @returns {Object} Processed breakdown
   */
  processBreakdown(payrollData, breakdown) {
    const result = {};

    for (const [key, source] of Object.entries(breakdown)) {
      if (source === 'calculated') {
        // Handle calculated values (e.g., travel allowance split)
        if (key === 'taxable' && breakdown.total) {
          const total = this.getValueFromSource(payrollData, breakdown.total);
          result[key] = Number(total * 0.8); // 80% taxable
        } else if (key === 'nonTaxable' && breakdown.total) {
          const total = this.getValueFromSource(payrollData, breakdown.total);
          result[key] = Number(total * 0.2); // 20% non-taxable
        }
      } else {
        result[key] = this.getValueFromSource(payrollData, source);
      }
    }

    return result;
  }

  /**
   * Calculate summary totals with proper travel allowance integration
   * @param {Object} payrollData - Payroll data
   * @param {Object} currentPeriod - Current period data
   * @returns {Object} Summary calculations
   */
  calculateSummary(payrollData, currentPeriod) {
    const basicSalary = Number(payrollData?.basicSalary || currentPeriod?.basicSalary || 0);

    // Calculate travel allowance components
    const travelAllowanceData = this.processTravelAllowanceEnhanced(payrollData);
    const travelAllowanceTotal = travelAllowanceData ? travelAllowanceData.totalAmount : 0;
    const travelAllowanceTaxable = travelAllowanceData ? travelAllowanceData.taxableAmount : 0;

    // Calculate other income components
    const commission = Number(payrollData?.commission || 0);
    const lossOfIncome = Number(payrollData?.lossOfIncome || 0);
    const accommodationBenefit = Number(payrollData?.accommodationBenefit || 0);

    // Log accommodation benefit for debugging
    if (accommodationBenefit > 0) {
      console.log(`Enhanced Calculator: Accommodation benefit included: R ${accommodationBenefit.toFixed(2)}`);
    } else {
      console.log('Enhanced Calculator: No accommodation benefit (removed or not set)');
    }

    // Calculate total gross pay (includes full travel allowance)
    const totalGrossPay = basicSalary + travelAllowanceTotal + commission + lossOfIncome + accommodationBenefit;

    // Calculate total taxable income (includes only taxable portion of travel allowance)
    const totalTaxableIncome = basicSalary + travelAllowanceTaxable + commission + accommodationBenefit;
    // Note: lossOfIncome is typically non-taxable

    // Calculate enhanced deductions based on taxable income
    const enhancedDeductions = this.calculateEnhancedDeductions(
      totalTaxableIncome,
      payrollData,
      currentPeriod
    );

    // Calculate net pay
    const netPay = totalGrossPay - enhancedDeductions.total;

    return {
      grossPay: Number(totalGrossPay.toFixed(2)),
      totalDeductions: Number(enhancedDeductions.total.toFixed(2)),
      netPay: Number(netPay.toFixed(2)),
      basicSalary: Number(basicSalary.toFixed(2)),

      // Enhanced breakdown
      breakdown: {
        totalTaxableIncome: Number(totalTaxableIncome.toFixed(2)),
        travelAllowance: {
          total: travelAllowanceTotal,
          taxable: travelAllowanceTaxable,
          nonTaxable: travelAllowanceTotal - travelAllowanceTaxable
        },
        otherIncome: {
          commission: commission,
          lossOfIncome: lossOfIncome,
          accommodationBenefit: accommodationBenefit
        },
        deductions: enhancedDeductions
      },

      source: currentPeriod ? 'enhanced_with_period' : 'enhanced_calculated'
    };
  }

  /**
   * Calculate enhanced deductions based on total taxable income
   * @param {number} totalTaxableIncome - Total taxable income including travel allowance
   * @param {Object} payrollData - Payroll data
   * @param {Object} currentPeriod - Current period data
   * @returns {Object} Enhanced deductions breakdown
   */
  calculateEnhancedDeductions(totalTaxableIncome, payrollData, currentPeriod) {
    // Use existing period deductions as base, but recalculate if travel allowance affects them
    const existingPAYE = Number(currentPeriod?.PAYE || 0);
    const existingUIF = Number(currentPeriod?.UIF || 0);
    const existingSDL = Number(currentPeriod?.SDL || 0);

    // Calculate enhanced PAYE based on total taxable income
    // This is an approximation - in production, you'd use the full PAYE calculation
    const basicSalary = Number(payrollData?.basicSalary || 0);
    const travelAllowanceData = this.processTravelAllowanceEnhanced(payrollData);
    const additionalTaxableIncome = travelAllowanceData ? travelAllowanceData.taxableAmount : 0;

    // Estimate additional PAYE from travel allowance (rough calculation)
    // In production, this should use the full calculateEnhancedPAYE function
    const estimatedAdditionalPAYE = additionalTaxableIncome * 0.25; // Approximate 25% tax rate
    const enhancedPAYE = existingPAYE + estimatedAdditionalPAYE;

    // UIF calculation on total income (capped at monthly maximum)
    const UIF_RATE = 0.01;
    const MAX_MONTHLY_UIF = 177.12;
    const enhancedUIF = Math.min(totalTaxableIncome * UIF_RATE, MAX_MONTHLY_UIF);

    // SDL calculation (1% of total remuneration for companies with payroll > R500k annually)
    const enhancedSDL = totalTaxableIncome * 0.01;

    // Other deductions from payroll data
    const medicalAidEmployee = Number(payrollData?.medicalAid?.employeeContribution || 0);
    const pensionFund = Number(payrollData?.pensionFund?.employeeContribution || 0);
    const garnishee = Number(payrollData?.garnishee || 0);
    const maintenanceOrder = Number(payrollData?.maintenanceOrder || 0);

    const totalDeductions = enhancedPAYE + enhancedUIF + enhancedSDL +
                           medicalAidEmployee + pensionFund + garnishee + maintenanceOrder;

    return {
      paye: Number(enhancedPAYE.toFixed(2)),
      uif: Number(enhancedUIF.toFixed(2)),
      sdl: Number(enhancedSDL.toFixed(2)),
      medicalAidEmployee: Number(medicalAidEmployee.toFixed(2)),
      pensionFund: Number(pensionFund.toFixed(2)),
      garnishee: Number(garnishee.toFixed(2)),
      maintenanceOrder: Number(maintenanceOrder.toFixed(2)),
      total: Number(totalDeductions.toFixed(2)),

      // Show the impact of travel allowance
      travelAllowanceImpact: {
        additionalPAYE: Number(estimatedAdditionalPAYE.toFixed(2)),
        description: `Additional PAYE due to travel allowance taxable portion`
      }
    };
  }

  /**
   * Get value from nested object path
   * @param {Object} obj - Source object
   * @param {string} path - Dot-notation path
   * @returns {*} Value at path
   */
  getValueFromSource(obj, path) {
    if (!obj || !path) return 0;

    const parts = path.split('.');
    let value = obj;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return 0;
      }
    }

    return value;
  }

  /**
   * Format currency for display
   * @param {number} value - Numeric value
   * @returns {string} Formatted currency string
   */
  formatCurrency(value) {
    const num = Number(value || 0);
    return `R ${num.toFixed(2)}`;
  }

  /**
   * Create fallback data structure
   * @param {Object} payrollData - Original payroll data
   * @param {Object} employee - Employee data
   * @param {Object} currentPeriod - Current period data
   * @returns {Object} Fallback data structure
   */
  createFallbackData(payrollData, employee, currentPeriod) {
    return {
      original: { payrollData, employee, currentPeriod },
      categories: { income: [], allowances: [], benefits: [], deductions: [] },
      summary: this.calculateSummary(payrollData, currentPeriod),
      metadata: {
        enhanced: false,
        fallback: true,
        timestamp: new Date().toISOString()
      }
    };
  }
}

module.exports = CalculatorEnhancer;
