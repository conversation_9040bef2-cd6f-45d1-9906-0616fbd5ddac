#!/usr/bin/env node

/**
 * Test script to verify logout redirect URLs have been fixed
 * This script checks that all logout functionality redirects to /login instead of /auth/login
 */

const fs = require('fs');
const path = require('path');

function testLogoutRedirects() {
  console.log('🔍 Testing Logout Redirect URLs...\n');
  
  const checks = [];
  const filesToCheck = [
    {
      path: 'routes/auth.js',
      patterns: [
        { search: 'res.redirect("/login")', description: 'Main logout route redirect' },
        { search: 'res.redirect("/login")', description: 'Auth logout route redirect' },
        { search: 'failureRedirect: "/login"', description: 'Login failure redirect' },
      ],
      antiPatterns: [
        { search: 'res.redirect("/auth/login")', description: 'Old auth/login redirect' },
        { search: 'failureRedirect: "/auth/login"', description: 'Old auth/login failure redirect' },
      ]
    },
    {
      path: 'views/partials/header.ejs',
      patterns: [
        { search: 'href="/logout"', description: 'Header logout link' }
      ],
      antiPatterns: [
        { search: 'href="/auth/logout"', description: 'Old auth/logout link' }
      ]
    },
    {
      path: 'views/employeeDashboard.ejs',
      patterns: [
        { search: 'href="/logout"', description: 'Employee dashboard logout link' }
      ],
      antiPatterns: [
        { search: 'href="/auth/logout"', description: 'Old auth/logout link' }
      ]
    },
    {
      path: 'public/hidePay.js',
      patterns: [
        { search: 'res.redirect("/login")', description: 'Legacy logout redirect' },
        { search: 'failureRedirect: "/login"', description: 'Legacy login failure redirect' }
      ],
      antiPatterns: [
        { search: 'res.redirect("/auth/login")', description: 'Old auth/login redirect' },
        { search: 'failureRedirect: "/auth/login"', description: 'Old auth/login failure redirect' }
      ]
    },
    {
      path: 'middleware/auth.js',
      patterns: [
        { search: "return '/login'", description: 'Middleware error redirect' }
      ],
      antiPatterns: [
        { search: "return '/auth/login'", description: 'Old auth/login error redirect' }
      ]
    }
  ];
  
  let allTestsPassed = true;
  
  filesToCheck.forEach(fileCheck => {
    console.log(`\n📁 Checking ${fileCheck.path}:`);
    
    try {
      const filePath = path.join(__dirname, fileCheck.path);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for required patterns
      fileCheck.patterns.forEach(pattern => {
        if (content.includes(pattern.search)) {
          console.log(`  ✅ ${pattern.description}: Found`);
          checks.push(`✅ ${fileCheck.path}: ${pattern.description}`);
        } else {
          console.log(`  ❌ ${pattern.description}: NOT FOUND`);
          checks.push(`❌ ${fileCheck.path}: ${pattern.description} - MISSING`);
          allTestsPassed = false;
        }
      });
      
      // Check for anti-patterns (should NOT be present)
      fileCheck.antiPatterns.forEach(antiPattern => {
        if (!content.includes(antiPattern.search)) {
          console.log(`  ✅ ${antiPattern.description}: Correctly removed`);
          checks.push(`✅ ${fileCheck.path}: ${antiPattern.description} - Removed`);
        } else {
          console.log(`  ❌ ${antiPattern.description}: STILL PRESENT`);
          checks.push(`❌ ${fileCheck.path}: ${antiPattern.description} - STILL EXISTS`);
          allTestsPassed = false;
        }
      });
      
    } catch (error) {
      console.log(`  ❌ Error reading file: ${error.message}`);
      checks.push(`❌ ${fileCheck.path}: Error reading file`);
      allTestsPassed = false;
    }
  });
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('LOGOUT REDIRECT TEST SUMMARY');
  console.log('='.repeat(60));
  
  checks.forEach(check => console.log(check));
  
  console.log('\n' + '='.repeat(60));
  
  if (allTestsPassed) {
    console.log('🎉 ALL LOGOUT REDIRECT TESTS PASSED!');
    console.log('\nKey fixes verified:');
    console.log('• Main logout routes redirect to /login');
    console.log('• Header logout link points to /logout');
    console.log('• Employee dashboard logout link points to /logout');
    console.log('• Login failure redirects go to /login');
    console.log('• Legacy routes updated to use /login');
    console.log('• Middleware error redirects go to /login');
    console.log('• All /auth/login references removed');
    
    console.log('\nExpected behavior:');
    console.log('• Users clicking logout will be redirected to /login');
    console.log('• Login failures will redirect to /login');
    console.log('• Session expiration will redirect to /login');
    console.log('• No broken redirect loops');
    console.log('• Consistent logout experience across all pages');
    
    return true;
  } else {
    console.log('❌ SOME LOGOUT REDIRECT TESTS FAILED');
    console.log('\nPlease review the failed checks above and fix any remaining issues.');
    return false;
  }
}

// Additional function to test for any remaining /auth/login references
function scanForRemainingAuthLoginReferences() {
  console.log('\n🔍 Scanning for any remaining /auth/login references...\n');
  
  const filesToScan = [
    'routes/auth.js',
    'views/partials/header.ejs',
    'views/employeeDashboard.ejs',
    'public/hidePay.js',
    'middleware/auth.js',
    'config/auth.js',
    'utils/jwtUtils.js'
  ];
  
  let foundReferences = [];
  
  filesToScan.forEach(filePath => {
    try {
      const fullPath = path.join(__dirname, filePath);
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        if (line.includes('/auth/login') && !line.includes('//') && !line.includes('*')) {
          foundReferences.push({
            file: filePath,
            line: index + 1,
            content: line.trim()
          });
        }
      });
    } catch (error) {
      // File might not exist, skip
    }
  });
  
  if (foundReferences.length === 0) {
    console.log('✅ No remaining /auth/login references found!');
  } else {
    console.log('⚠️  Found remaining /auth/login references:');
    foundReferences.forEach(ref => {
      console.log(`  ${ref.file}:${ref.line} - ${ref.content}`);
    });
  }
  
  return foundReferences.length === 0;
}

// Run tests
if (require.main === module) {
  const redirectTestsPassed = testLogoutRedirects();
  const noRemainingRefs = scanForRemainingAuthLoginReferences();
  
  const allTestsPassed = redirectTestsPassed && noRemainingRefs;
  
  console.log('\n' + '='.repeat(60));
  console.log(`FINAL RESULT: ${allTestsPassed ? 'ALL TESTS PASSED ✅' : 'SOME TESTS FAILED ❌'}`);
  console.log('='.repeat(60));
  
  process.exit(allTestsPassed ? 0 : 1);
}

module.exports = { testLogoutRedirects, scanForRemainingAuthLoginReferences };
