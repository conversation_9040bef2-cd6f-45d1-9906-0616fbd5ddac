# PandaPayroll Email Reminder System - Troubleshooting Guide

## 🎯 Expected Behavior

### When "Send Email Reminders" is clicked:

1. **System queries** for events matching:
   - `reminderSettings.enabled: true`
   - `reminderSettings.emailReminder: true` 
   - `status: { $ne: "completed" }`
   - `date: { $gte: [current date] }`

2. **For each event**, checks if reminder should be sent today:
   - Calculates days until event
   - Checks if today matches any value in `reminderSettings.daysBeforeEvent`
   - Verifies reminder hasn't been sent for this day already

3. **Finds recipients** for each event:
   - Users assigned to event (`assignedTo` field)
   - Company administrators (owner, admin, manager, hr, payroll)
   - Fallback to company owner

4. **Sends personalized emails** with:
   - Professional template with company branding
   - Priority-based subject lines and styling
   - Context-aware content based on event type
   - Recipient role information

5. **Records delivery status** in `emailReminders` array
6. **Returns summary** with sent/failed counts

## 🔍 Current System Status (Based on Diagnostic)

### ✅ Database Analysis
- **Total Events:** 31
- **Events with Email Reminders Enabled:** 24
- **Events That Should Send Reminders Today:** 5

### 📧 Events Ready for Reminders Today
1. **TEST: Custom Event Due Today** (0 days before)
2. **TEST: EMP201 Due Tomorrow** (1 day before)  
3. **TEST: IRP5 Due in 3 Days** (3 days before)
4. **TEST: SDL Due in 7 Days** (7 days before)
5. **TEST: UIF Due in 14 Days** (14 days before)

### 👥 Recipient Analysis
- **Surevest Company** (test events): 0 admin users found ⚠️
- **Panda Solutions Company** (real events): 1 admin user found ✅

## 🛠️ Troubleshooting Steps

### Step 1: Test Email Reminders
1. Open dashboard: http://localhost:3002/dashboard
2. Click "Reminders" dropdown → "Send Email Reminders"
3. Check response message for sent/failed counts

### Step 2: Check Server Logs
Monitor server console for detailed logging:
```
📧 Email reminder job started
Getting recipients for event: [Event Title] (Company: [Company ID])
Found [X] assigned users
Looking for company administrators for company: [Company ID]
Found [X] potential company administrators
Added company [role]: [email]
Final recipient list: [X] recipients
Sending personalized reminder to [email] ([role]) for event: [Event Title]
✅ Reminder sent successfully to [email] for event [Event ID]
```

### Step 3: Verify Email Configuration
Check environment variables:
```bash
EMAIL_USER=<EMAIL>
EMAIL_PASS=[password]
EMAIL_FROM_NAME=PandaPayroll Compliance
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
```

### Step 4: Check Email Delivery
1. Check recipient email inboxes
2. Look for emails with subjects like:
   - "🚨 URGENT: [Event Title] - Due tomorrow | [Company Name]"
   - "⚠️ IMPORTANT: [Event Title] - Due in 3 days | [Company Name]"
   - "📋 REMINDER: [Event Title] - Due in 7 days | [Company Name]"

## 🔧 Common Issues & Solutions

### Issue 1: "Found 0 events needing reminders"

**Possible Causes:**
- No events in database
- Events don't have reminder settings enabled
- Events are marked as completed
- No events match today's reminder schedule

**Solutions:**
1. Run diagnostic: `node scripts/diagnose-email-reminders.js`
2. Create test events: `node scripts/create-test-events.js`
3. Check event reminder settings in database
4. Verify event dates and status

### Issue 2: "0 sent, 0 failed" but events exist

**Possible Causes:**
- No recipients found for events
- Email configuration issues
- SMTP authentication problems

**Solutions:**
1. Check company has admin users with email addresses
2. Verify SMTP credentials and settings
3. Check server logs for detailed error messages
4. Test email configuration manually

### Issue 3: Emails not received

**Possible Causes:**
- Emails in spam/junk folder
- SMTP delivery issues
- Invalid recipient email addresses
- Email server blocking

**Solutions:**
1. Check spam/junk folders
2. Verify email addresses are valid
3. Check SMTP server logs
4. Test with different email addresses

## 📊 Manual Testing Commands

### Run Diagnostic
```bash
cd /path/to/PandaPayroll
node scripts/diagnose-email-reminders.js
```

### Create Test Events
```bash
node scripts/create-test-events.js
```

### Clean Up Test Events
```bash
node scripts/create-test-events.js cleanup
```

### Check Database Events
```javascript
// In MongoDB shell or Node.js
db.payrollcalendarevents.find({
  "reminderSettings.enabled": true,
  "reminderSettings.emailReminder": true,
  "status": { $ne: "completed" },
  "date": { $gte: new Date() }
}).count()
```

## 🎯 Expected Test Results

With the current test events, clicking "Send Email Reminders" should:

1. **Find 5 events** needing reminders
2. **Send emails** to available recipients:
   - Test events (Surevest): May have 0 recipients if no admin users
   - Real events (Panda Solutions): Should <NAME_EMAIL>
3. **Return message**: "Email reminders processed successfully! ✅ Sent: X, ❌ Failed: Y, 📊 Total Processed: 5"

## 🔍 Debugging Tips

### Enable Detailed Logging
The system includes comprehensive logging. Watch for:
- Event query results
- Recipient discovery process
- Email generation and sending
- Delivery status updates

### Check Email Headers
Sent emails include tracking headers:
- X-Event-Type: [event type]
- X-Event-Priority: [priority level]
- X-Company-ID: [company ID]
- Message-ID: [unique identifier]

### Verify Database Updates
After sending, check `emailReminders` array in events:
```javascript
db.payrollcalendarevents.findOne({title: "TEST: EMP201 Due Tomorrow"}).emailReminders
```

## 📧 Email Template Features

### Professional Design
- PandaPayroll branding with gradient headers
- Responsive layout for all devices
- Priority-based color coding
- Company-specific personalization

### Content Features
- Personalized greetings with recipient names
- Role-based context (why they received the email)
- Event details with compliance information
- Call-to-action buttons linking to dashboard
- Professional footer with contact information

## ✅ Success Indicators

### System Working Correctly When:
1. Diagnostic shows events needing reminders
2. "Send Email Reminders" returns positive sent count
3. Recipients receive professional, branded emails
4. Database records delivery status
5. No duplicate emails sent for same day/event
6. Server logs show successful processing

### Next Steps After Testing:
1. Verify automated daily schedule (8:00 AM SAST)
2. Test with real compliance events
3. Monitor email deliverability
4. Set up email tracking and analytics
5. Configure backup notification methods
